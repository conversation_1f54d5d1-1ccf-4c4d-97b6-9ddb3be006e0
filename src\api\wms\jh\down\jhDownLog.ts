/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface JhDownLog extends BasicModel<JhDownLog> {
  cid?: string; // cid
  fitemid?: string; // 商品内码
  fitemno?: string; // 商品编码
  fitemname?: string; // 商品名称
  cposCode?: string; // 货位编码
  cposName?: string; // 货位名称
  afCposCode?: string; // 下后货位编码
  afCposName?: string; // 下后货位名称
  fqty?: number; // 数量
  cfree1?: string; // 形象刊
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
  type?: string; // 下架类型
  createByName?: string; // 制单人名称
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
}

export const jhDownLogList = (params?: JhDownLog | any) =>
  defHttp.get<JhDownLog>({ url: adminPath + '/wms/jh/down/jhDownLog/list', params });

export const jhDownLogListData = (params?: JhDownLog | any) =>
  defHttp.post<Page<JhDownLog>>({ url: adminPath + '/wms/jh/down/jhDownLog/listData', params });

export const jhDownLogForm = (params?: JhDownLog | any) =>
  defHttp.get<JhDownLog>({ url: adminPath + '/wms/jh/down/jhDownLog/form', params });

export const jhDownLogSave = (params?: any, data?: JhDownLog | any) =>
  defHttp.postJson<JhDownLog>({ url: adminPath + '/wms/jh/down/jhDownLog/save', params, data });

export const jhDownLogDelete = (params?: JhDownLog | any) =>
  defHttp.get<JhDownLog>({ url: adminPath + '/wms/jh/down/jhDownLog/delete', params });
