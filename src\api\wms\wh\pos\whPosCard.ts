/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPosCard extends BasicModel<WhPosCard> {
  invCode?: string; // 物料编码
  cfree1?: string; // 形象刊
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项2
  posCode?: string; // 货位编码
  whCode?: string; // 仓库编码
  iqty?: number; // 数量
  ctype?: string; // 方向
  sourceId?: string; // 来源ID
  busType?: string; // 业务类型
  companyCode?: string; // 公司
}

export const whPosCardList = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/wms/wh/pos/whPosCard/list', params });

export const whPosCardListData = (params?: WhPosCard | any) =>
  defHttp.post<Page<WhPosCard>>({ url: adminPath + '/wms/wh/pos/whPosCard/listData', params });

export const whPosCardForm = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/wms/wh/pos/whPosCard/form', params });

export const whPosCardSave = (params?: any, data?: WhPosCard | any) =>
  defHttp.postJson<WhPosCard>({ url: adminPath + '/wms/wh/pos/whPosCard/save', params, data });

export const whPosCardDelete = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/wms/wh/pos/whPosCard/delete', params });

export const whPosCardFindActual = (params?: WhPosCard | any) =>
  defHttp.post<WhPosCard>({ url: adminPath + '/wms/wh/pos/whPosInvStock/findActual', params });
