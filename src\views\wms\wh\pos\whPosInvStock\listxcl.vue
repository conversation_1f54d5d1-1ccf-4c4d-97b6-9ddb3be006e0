<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <!-- <a-button type="primary" @click="handleForm({})" v-auth="'wms:wh:pos:whPosInvStock:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.invCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhPosWhPosInvStockList">
  import { unref, onMounted, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whPosInvStockDelete, whPosInvStockListSumData } from '/@/api/wms/wh/pos/whPosInvStock';
  import { companyTreeData } from '/@/api/sys/company';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { Table } from 'ant-design-vue';
  import { useGlobSetting } from "/@/hooks/setting";
  import { downloadByUrl } from "/@/utils/file/download";
  const userStore = useUserStore();

  const { t } = useI18n('wms.wh.pos.whPosInvStock');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('货位存量表管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('公司'),
        field: 'companyCode',
        fieldLabel: 'company.companyName',
        component: 'TreeSelect',
        componentProps: {
          api: companyTreeData,
          allowClear: true,
          immediate: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('商品编码'),
        field: 'basInv.viewCode',
        component: 'Input',
      },
      {
        label: t('商品名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('仓库'),
        field: 'basWare.cwhname',
        component: 'Input',
      },
      {
        label: t('形象刊'),
        field: 'freeVO.cfree1',
        component: 'Input',
      },
      // 排除0库存 指为1和0，默认选中为1
      {
        label: t(''),
        field: 'notIsZero',
        component: 'CheckboxGroup',
        defaultValue: ['1'],
        componentProps: {
          options: [
            { label: '排除0库存', value: '1' },
          ],
        },
      }
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('公司'),
      dataIndex: 'company.companyName',
      key: 'a.company_code',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('商品编码'),
      dataIndex: 'basInv.viewCode',
      key: 'inv.view_code',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('商品名称'),
      dataIndex: 'basInv.invName',
      key: 'inv.inv_name',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('现存量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'right',
      showSummary: true,
      showTotalSummary: true,
    },
    {
      title: t('仓库'),
      dataIndex: 'basWare.cwhname',
      key: 'a.whcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('形象刊'),
      dataIndex: 'freeVO.cfree1',
      key: 'a.cfree1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项2'),
      dataIndex: 'freeVO.cfree2',
      key: 'a.cfree2',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('自由项3'),
      dataIndex: 'freeVO.cfree3',
      key: 'a.cfree3',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑货位存量表'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'wms:wh:pos:whPosInvStock:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除货位存量表'),
        popConfirm: {
          title: t('是否确认删除货位存量表'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms:wh:pos:whPosInvStock:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm, getDataSource }] = useTable({
    api: whPosInvStockListSumData,
    beforeFetch: (params) => {
      // 处理排除0库存字段：CheckboxGroup 返回数组，选中时为['1']，未选中时为[]
      if (params.notIsZero !== undefined) {
        if (Array.isArray(params.notIsZero)) {
          // CheckboxGroup 返回数组，如果包含'1'则为1，否则为0
          params.notIsZero = params.notIsZero.includes('1') ? 1 : 0;
        } else {
          // 兼容其他可能的值类型
          params.notIsZero = params.notIsZero ? 1 : 0;
        }
      }
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    immediate: false,
    showCustomSummary: true,
    showTotalCustomSummary: true,
  });

  const totalIqty = computed(() => {
    // 计算总报工数
    const tableData = getDataSource();
    console.log('tableData', tableData);
    // const tableData = getDataSource.value;
    let sum = tableData.reduce((total, item) => total + (item.iqty || 0), 0);
    return sum;
  });
  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await whPosInvStockDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  onMounted(async () => {
    handleCompany();
  });

  async function handleCompany() {
    await getForm().setFieldsValue({
      companyCode: userStore.getProjecte.code,
    });
    reload();
    await getForm().setFieldsValue({
      companyCode: userStore.getProjecte.code,
    });
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wms/wh/pos/whPosInvStock/listSumDataExport',
      params: getForm().getFieldsValue(),
    });
  }
</script>
