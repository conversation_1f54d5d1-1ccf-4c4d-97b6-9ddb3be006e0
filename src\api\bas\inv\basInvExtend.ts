/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasInvExtend extends BasicModel<BasInvExtend> {
  invCode?: string; // 物料ID
}

export const basInvExtendList = (params?: BasInvExtend | any) =>
  defHttp.get<BasInvExtend>({ url: adminPath + '/bas/inv/basInvExtend/list', params });

export const basInvExtendListData = (params?: BasInvExtend | any) =>
  defHttp.post<Page<BasInvExtend>>({ url: adminPath + '/bas/inv/basInvExtend/listData', params });

export const basInvExtendForm = (params?: BasInvExtend | any) =>
  defHttp.get<BasInvExtend>({ url: adminPath + '/bas/inv/basInvExtend/form', params });

export const basInvExtendSave = (params?: any, data?: BasInvExtend | any) =>
  defHttp.postJson<BasInvExtend>({ url: adminPath + '/bas/inv/basInvExtend/save', params, data });

export const basInvExtendDelete = (params?: BasInvExtend | any) =>
  defHttp.get<BasInvExtend>({ url: adminPath + '/bas/inv/basInvExtend/delete', params });
