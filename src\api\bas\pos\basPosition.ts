/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasPosition extends TreeModel<BasPosition> {
  posCode?: string; // 货位编码
  posName?: string; // 货位名称
  whcode?: string; // 仓库编码
  volumeQty?: number; // 容积数量
  volumeWeight?: number; // 容积重量
  bzhongZhuan?: any; // 是否中转货位
}

export const basPositionList = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/list', params });

export const basPositionListData = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({ url: adminPath + '/bas/pos/basPosition/listData', params });

export const basPositionForm = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/form', params });

export const basPositionCreateNextNode = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/createNextNode', params });

export const basPositionSave = (params?: any, data?: BasPosition | any) =>
  defHttp.postJson<BasPosition>({ url: adminPath + '/bas/pos/basPosition/save', params, data });

export const basPositionImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/pos/basPosition/importData',
      onUploadProgress,
    },
    params,
  );

export const basPositionDelete = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/delete', params });

export const basPositionTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/pos/basPosition/treeData', params });

// 根据上级编码获取对应仓库
export const basPositionWareByCode = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/getWareByCode', params });

export const basPositionListDataLeaf = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({ url: adminPath + '/bas/pos/basPosition/listDataLeaf', params });

// /bas/pos/basPosition/findPartitionInfo
export const findPartitionInfo = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({
    url: adminPath + '/bas/pos/basPosition/findPartitionInfo',
    params,
  });

export const findLocationInfo = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({ url: adminPath + '/bas/pos/basPosition/findLocationInfo', params });
export const findBookInfo = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({
    url: adminPath + '/wms/wh/pos/whPosInvStock/findBookInfo',
    params,
  });
