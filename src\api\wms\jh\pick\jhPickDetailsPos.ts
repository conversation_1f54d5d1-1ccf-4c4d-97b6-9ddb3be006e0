/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface JhPickDetailsPos extends BasicModel<JhPickDetailsPos> {
  hid?: string; // 明细ID
  cposCode?: string; // 货位编码
  cposName?: string; // 货位名称
  fqty?: number; // 数量
  sumQty?: number; // 累计下架数
  jhStatus?: string; // 状态
}

export const jhPickDetailsPosList = (params?: JhPickDetailsPos | any) =>
  defHttp.get<JhPickDetailsPos>({ url: adminPath + '/wms/jh/pick/jhPickDetailsPos/list', params });

export const jhPickDetailsPosListData = (params?: JhPickDetailsPos | any) =>
  defHttp.post<Page<JhPickDetailsPos>>({ url: adminPath + '/wms/jh/pick/jhPickDetailsPos/listData', params });

export const jhPickDetailsPosForm = (params?: JhPickDetailsPos | any) =>
  defHttp.get<JhPickDetailsPos>({ url: adminPath + '/wms/jh/pick/jhPickDetailsPos/form', params });

export const jhPickDetailsPosSave = (params?: any, data?: JhPickDetailsPos | any) =>
  defHttp.postJson<JhPickDetailsPos>({ url: adminPath + '/wms/jh/pick/jhPickDetailsPos/save', params, data });

export const jhPickDetailsPosDelete = (params?: JhPickDetailsPos | any) =>
  defHttp.get<JhPickDetailsPos>({ url: adminPath + '/wms/jh/pick/jhPickDetailsPos/delete', params });
