/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface JhPickDetailsInv extends BasicModel<JhPickDetailsInv> {
  hid?: string; // 明细ID
  cid?: string; // Cid
  fbillno?: string; // 发货单号
  forderno?: string; // 订单号
  fsupplyid?: number; // 购货单位内码
  fsupplierno?: string; // 购货单位编码
  fsuppliername?: string; // 购货单位名称
  invCode?: string; // 商品编码
  fqty?: number; // 数量
  cfree1?: string; // 形象刊
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
}

export const jhPickDetailsInvList = (params?: JhPickDetailsInv | any) =>
  defHttp.get<JhPickDetailsInv>({ url: adminPath + '/wms/jh/pick/jhPickDetailsInv/list', params });

export const jhPickDetailsInvListData = (params?: JhPickDetailsInv | any) =>
  defHttp.post<Page<JhPickDetailsInv>>({ url: adminPath + '/wms/jh/pick/jhPickDetailsInv/listData', params });

export const jhPickDetailsInvForm = (params?: JhPickDetailsInv | any) =>
  defHttp.get<JhPickDetailsInv>({ url: adminPath + '/wms/jh/pick/jhPickDetailsInv/form', params });

export const jhPickDetailsInvSave = (params?: any, data?: JhPickDetailsInv | any) =>
  defHttp.postJson<JhPickDetailsInv>({ url: adminPath + '/wms/jh/pick/jhPickDetailsInv/save', params, data });

export const jhPickDetailsInvDelete = (params?: JhPickDetailsInv | any) =>
  defHttp.get<JhPickDetailsInv>({ url: adminPath + '/wms/jh/pick/jhPickDetailsInv/delete', params });

// 
export const jhPickDetailsbachSave = (params?: any, data?: JhPickDetailsInv | any) =>
  defHttp.postJson<JhPickDetailsInv>({ url: adminPath + '/wms/jh/pick/jhPickMain/bachSave', params, data });
