/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasCus extends BasicModel<BasCus> {
  code?: string; // 客户编码
  name?: string; // 客户名称
  abbName?: string; // 客户简称
  clsCode?: string; // 客户分类
}

export const basCusList = (params?: BasCus | any) =>
  defHttp.get<BasCus>({ url: adminPath + '/bas/cus/list', params });

export const basCusListData = (params?: BasCus | any) =>
  defHttp.post<Page<BasCus>>({ url: adminPath + '/bas/cus/listData', params });

export const basCusForm = (params?: BasCus | any) =>
  defHttp.get<BasCus>({ url: adminPath + '/bas/cus/form', params });

export const basCusSave = (params?: any, data?: BasCus | any) =>
  defHttp.postJson<BasCus>({ url: adminPath + '/bas/cus/save', params, data });

export const basCusDelete = (params?: BasCus | any) =>
  defHttp.get<BasCus>({ url: adminPath + '/bas/cus/delete', params });

export const loginUserCusList = (params?: BasCus | any) =>
  defHttp.get<BasCus>({ url: adminPath + '/bas/cus/basUserCus/findLoginUserCusList', params });

export const basCusTreeData = (params?: BasCus | any) =>
  defHttp.post<Page<BasCus>>({ url: adminPath + '/bas/cus/treeData', params });
