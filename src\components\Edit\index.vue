<template>
    <section>
        <div class="tab-content">
            <h2>{{ type && list[type]['tit'] }}</h2>
            <!-- <div class="tab" v-if="type != 'info'">
                <span
                    v-for="(val, key, index) in tabType"
                    :key="index"
                    @click="tab(key)"
                    :class="{active: val}"
                ><i class="el-icon-s-data"></i> {{ key }}</span>
            </div> -->
        </div>
        <component 
            :is="type && list[type]['com']" 
            :data="props.data"
            @changeTab="tab"
        ></component>
    </section>
</template>

<script lang="ts" setup>
import Info from "/@/components/Edit/Info.vue"
import Images from "/@/components/Edit/Image.vue"
import Product from "/@/components/Edit/Product.vue"
import ProductSell from "/@/components/Edit/ProductSell.vue"
import Login from "/@/components/Edit/Login.vue"
import HomeBanner from "/@/components/Edit/HomeBanner.vue"
import HomeClassify from "/@/components/Edit/HomeClassify.vue"
import TitleImages from "/@/components/Edit/TitleImages.vue"

import { reactive, ref, computed ,onMounted} from 'vue';
const props = defineProps({
    data: { type: Object, default: {} },
  });

  let list:any = reactive({
                'info': {
                    tit: '页面信息',
                    com: Info
                },
                'images': {
                    tit: '图片',
                    com: Images
                },
                'banner': {
                    tit: '轮播图',
                    com: Images
                },
                'product': {
                    tit: '商品',
                    com: Product
                },
                'productSell': {
                    tit: '促销',
                    com: ProductSell
                },
                'login': {
                    tit: '背景图',
                    com: Login
                },
                'homeBanner': {
                    tit: '首页轮播图',
                    com: HomeBanner
                },
                'homeClassify': {
                    tit: '产品系列',
                    com: HomeClassify
                },
                'titleImages': {
                    tit: '标题图片',
                    com: TitleImages
                },
                'footImages': {
                    tit: '底部图片',
                    com: TitleImages
                },

                
                
                // 'classify': {
                //     tit: '商品种类',
                //     com: Images
                // },
            });
  let tabType:any = reactive({
                1: true,
                2: false,
                3: false
            });
  let type:any = ref('');
  onMounted(() => {
    type.value = props.data.type
        if (props.data.tabType) {
            tab(props.data.tabType)
        }
  });
  function tab(key) {
            for (let i in tabType) {
                if (key == i) {
                    tabType[key] = true
                    props.data.tabType=key
                    // =========================================================
                } else {
                    tabType[i] = false
                }
            }
        }
</script>

<style scoped lang="less">
section{
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
}
.tab-content{
    padding: 0 15px;
    h2{
        font-size: 16px;
        color: #333;
    }
    .tab{
        display: flex;
        justify-content: space-around;
        border: 1px solid #ddd;
        border-radius: 6px;
        span{
            width: 33.33%;
            text-align: center;
            font-size: 14px;
            color: #666;
            display: block;
            height: 36px;
            line-height: 36px;
            cursor: pointer;
            &.active{
                color: #fff;
                background: #409eff;
                border-radius: 2px;
            }
            &:nth-of-type(2) {
                border-left: 1px solid #ddd;
                border-right: 1px solid #ddd;
            }
        }
    }
}
</style>