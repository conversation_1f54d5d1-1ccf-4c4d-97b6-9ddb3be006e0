/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { Page } from '../model/baseModel';
import { BpmTask } from './model';

const { adminPath } = useGlobSetting();

export const bpmMyTaskList = (params?: BpmTask | any) =>
  defHttp.post<Page<BpmTask>>({ url: adminPath + '/bpm/bpmMyTask/listData', params });

export const bpmMyTaskForm = (params?: BpmTask | any) => {
  params.addPrefix = false;
  return defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmMyTask/form', params });
};

export const bpmTaskDefForm = (params?: BpmTask | any) => {
  return defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTaskDef/form', params });
};
