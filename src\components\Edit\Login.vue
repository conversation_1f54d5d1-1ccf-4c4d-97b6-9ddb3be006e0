<template>
  <!-- Banner & Image 通用组件 -->
  <div class="image-content">
    <p class="desc">添加图片 (最多{{ len }}张),多加无效</p>

    <vuedraggable :list="list.data" v-if="list.data && list.data.length > 0" class="image-list">
      <template #item="{ element, index }">
        <li class="item">
          <div class="l-info">
            <p
              ><span class="sort">排序{{ index + 1 }}</span></p
            >
            <p>
              <span>名称：</span>
              <span class="text">{{ element && element.name }}</span>
            </p>
            <!-- <p>
              <span>链接：</span>
              <el-tooltip effect="dark" :content="element.link" placement="top" v-if="element.link">
                <span class="text" @click="urlPopup(index, element.link)">{{ element.link }}</span>
              </el-tooltip>
              <span v-else @click="urlPopup(index, '')" class="link">请输入跳转链接</span>
            </p> -->
          </div>
          <div class="r-image">
            <div @click="removeImage(index)" class="el-icon-close">
              <Icon icon="simple-line-icons:close" />
            </div>
            <!-- <span @click="removeImage(index)" class="el-icon-close"></span> -->
            <div class="image-box" @click="addImage(index)">
              <img :src="element && element.url" />

              <!-- <span @click="addImage(index)" class="el-icon-edit-outline"></span> -->
            </div>
          </div>
        </li>
      </template>
    </vuedraggable>
    <div v-if="list.data && list.data.length < len">
      <el-button type="primary" icon="el-icon-plus" @click="addImage(null)" class="add-image">
        添加图片
      </el-button>
    </div>
    <p class="size">（建议尺寸：{{ size }}）</p>

    <el-upload :http-request="upload" :show-file-list="false" multiple action style="display: none">
      <el-button ref="elupload" type="primary"> 添加图片 </el-button>
    </el-upload>
    <el-dialog title="请填写图片跳转链接" v-model="show" @close="close">
      <el-form label-width="100px">
        <el-form-item label="跳转链接">
          <el-input v-model="url"></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="confirm">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import { reactive, ref, computed, onMounted } from 'vue';
  import vuedraggable from 'vuedraggable';
  import { ElMessage } from 'element-plus';
  import { saveFile, getBase64WithFile } from '/@/api/shop/shop';

  const props = defineProps({
    data: { type: Object, default: {} },
  });
  let list: any = ref({});
  let url: any = ref('');
  let index: any = ref(null);
  let show: any = ref(false);
  let imageIndex: any = ref(null);
  let elupload: any = ref(null);

  const size = computed(() => {
    return list.value.type == 'login' ? '720*1080' : '750*400';
  });
  const len = computed(() => {
    return list.value.type == 'login' ? 1 : 8;
  });
  onMounted(() => {
    console.log(props.data, 'props.data=========');
    list.value = props.data;
  });

  function close() {
    show.value = false;
    url.value = '';
  }
  function confirm() {
    list.value['data'][index.value]['link'] = url.value;
    close();
  }
  function urlPopup(index1, link) {
    index.value = index1;
    url.value = link;
    show.value = true;
  }
  function removeImage(index) {
    list.value.data.splice(index, 1);
  }
  function addImage(index) {
    imageIndex.value = index;
    elupload.value?.$.vnode.el?.click();
  }

  async function upload(params) {
    console.log(22222222222);
    console.log(params, 'params======');

    const len = list.value.data.length;

    if (len >= len.value) {
      ElMessage.error(`最多添加${len.value}张图片!`);
      return;
    }
    const file = params.file,
      fileType = file.type;

    const verifyList = [
      {
        text: '只能上传图片格式png、jpg、gif!',
        result: fileType.indexOf('image') != -1,
      },
      {
        text: '只能上传大小小于5M',
        result: file.size / 1024 / 1024 < 5,
      },
    ];

    for (let item of verifyList) {
      if (!item.result) {
        ElMessage.error(item.text);
        return;
      }
    }
    await getBase64WithFile(file).then(async ({ result: fileUrl }) => {
      const res = await saveFile({
        base64: fileUrl,
        // id: list.value.id,
        fileName: file.name,
        uploadType: 'image',
      });

      file.fileUpload = res.fileUpload
      console.log(res);
      console.log(file.fileUpload,'file.fileUpload+++++');
    });

    const form = new FormData();
    form.append('file', file);
    form.append('clientType', 'multipart/form-data');

    const index = imageIndex.value;
    const data = {
      name: file.name,
      url: URL.createObjectURL(file),
      form,
      fileUpload:file.fileUpload?file.fileUpload:''
    };

    if (index !== null) {
      list.value.data[index] = data;
      // this.$set(list.data, index, data)
    } else {
      list.value.data.push(data);
    }
    console.log(list.value,'list.value=======');
    
  }
</script>

<style lang="less" scoped>
  .image-content {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%;
    overflow: hidden;
    .desc {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin: 18px 0;
      padding-bottom: 10px;
      border-bottom: 1px dashed #ddd;
    }
    .size {
      text-align: center;
      font-size: 12px;
      color: #f80b0bfe;
      margin-bottom: 0;
    }
    .add-image {
      width: calc(100% - 20px);
      height: 34px;
      line-height: 34px;
      padding: 0;
      font-size: 12px;
      margin-left: 10px;
      margin-top: 10px;
    }
    .image-list {
      margin: 0;
      padding: 0 10px;
      overflow: auto;
      &::-webkit-scrollbar-thumb {
        background: #dbdbdb;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-track {
        background: #f6f6f6;
        border-radius: 10px;
      }
      &::-webkit-scrollbar {
        width: 6px;
      }
      li {
        list-style: none;
        background: #f7f8f9;
        border-radius: 4px;
        padding: 6px 14px 10px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        &.disable div {
          pointer-events: none;
          user-select: none;
        }
        .l-info {
          font-size: 12px;
          padding-top: 8px;
          width: calc(100% - 70px);
          p {
            margin: 12px 0 0;
            white-space: nowrap;
            overflow: hidden;
            display: flex;
            .link {
              color: #1b8bff;
              cursor: pointer;
            }
            .text {
              white-space: nowrap;
              text-align: -webkit-auto;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
        .r-image {
          text-align: right;
          .el-icon-close {
            color: #999;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 6px;
            cursor: pointer;
            &:hover {
              color: red;
            }
          }
          .image-box {
            width: 70px;
            height: 70px;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              opacity: 0;
              transition: all 0.3s;
            }
            span {
              position: absolute;
              top: 50%;
              left: 50%;
              color: #fff;
              transform: translate(-50%, -50%);
              font-size: 20px;
              cursor: pointer;
              opacity: 0;
              transition: all 0.3s;
            }
            img {
              max-width: 100%;
            }
            &:hover {
              &::before,
              span {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
</style>
