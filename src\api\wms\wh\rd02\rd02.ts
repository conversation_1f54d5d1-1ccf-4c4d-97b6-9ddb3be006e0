/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rd02 extends BasicModel<Rd02> {
  code?: string; // 出库单号
  ddate?: string; // 出库日期
  whCode?: string; // 仓库编码
  busType?: string; // 业务类型
  bred?: string; // 红蓝标志
  companyCode?: string; // 公司
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
  rds02List?: any[]; // 子表列表
}

export const rd02List = (params?: Rd02 | any) =>
  defHttp.get<Rd02>({ url: adminPath + '/wh/rd02/rd02/list', params });

export const rd02ListData = (params?: Rd02 | any) =>
  defHttp.post<Page<Rd02>>({ url: adminPath + '/wh/rd02/rd02/listData', params });

export const rds02ListData = (params?: Rd02 | any) =>
  defHttp.post<Page<Rd02>>({ url: adminPath + '/wh/rd02/rd02/rds02ListData', params });

export const rd02Form = (params?: Rd02 | any) =>
  defHttp.get<Rd02>({ url: adminPath + '/wh/rd02/rd02/form', params });

export const rd02Save = (params?: any, data?: Rd02 | any) =>
  defHttp.postJson<Rd02>({ url: adminPath + '/wh/rd02/rd02/save', params, data });

export const rd02ImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/rd02/rd02/importData',
      onUploadProgress,
    },
    params,
  );

export const rd02Delete = (params?: Rd02 | any) =>
  defHttp.get<Rd02>({ url: adminPath + '/wh/rd02/rd02/delete', params });

export const batchAudit = (params?: Rd02 | any) =>
  defHttp.post<Rd02>({ url: adminPath + '/wh/rd02/rd02/batchAudit', params });

export const batchSend = (params?: Rd02 | any) =>
  defHttp.post<Rd02>({ url: adminPath + '/wh/rd02/rd02/batchSend', params });

export const batchUpdateDate = (params?: Rd02 | any) =>
  defHttp.post<Rd02>({ url: adminPath + '/wh/rd02/rd02/batchUpdateDate', params });

export const batchBack = (params?: Rd02 | any) =>
  defHttp.post<Rd02>({ url: adminPath + '/wh/rd02/rd02/batchBack', params });

// /wh/rd02/rd02/batchSendTl
export const batchSendTl = (params?: Rd02 | any) =>
  defHttp.post<Rd02>({ url: adminPath + '/wh/rd02/rd02/batchSendTl', params });
