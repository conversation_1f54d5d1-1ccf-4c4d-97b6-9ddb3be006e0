/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rd01 extends BasicModel<Rd01> {
  code?: string; // 入库单号
  ddate?: string; // 单据日期
  busType?: string; // bus_type
  bred?: string; // bred
  companyCode?: string; // company_code
  whCode?: string; // 仓库
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
  rds01List?: any[]; // 子表列表
}

export const rd01List = (params?: Rd01 | any) =>
  defHttp.get<Rd01>({ url: adminPath + '/wh/rd01/rd01/list', params });

export const rd01ListData = (params?: Rd01 | any) =>
  defHttp.post<Page<Rd01>>({ url: adminPath + '/wh/rd01/rd01/listData', params });

export const rds01ListData = (params?: Rd01 | any) =>
  defHttp.post<Page<Rd01>>({ url: adminPath + '/wh/rd01/rd01/rds01ListData', params });

export const rd01Form = (params?: Rd01 | any) =>
  defHttp.get<Rd01>({ url: adminPath + '/wh/rd01/rd01/form', params });

export const rd01Save = (params?: any, data?: Rd01 | any) =>
  defHttp.postJson<Rd01>({ url: adminPath + '/wh/rd01/rd01/save', params, data });

export const rd01ImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/rd01/rd01/importData',
      onUploadProgress,
    },
    params,
  );

export const rd01Delete = (params?: Rd01 | any) =>
  defHttp.get<Rd01>({ url: adminPath + '/wh/rd01/rd01/delete', params });


export const batchUpdateDate = (params?: Rd01 | any) =>
  defHttp.post<Rd01>({ url: adminPath + '/wh/rd01/rd01/batchUpdateDate', params });



