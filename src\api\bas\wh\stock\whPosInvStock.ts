/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPosInvStock extends BasicModel<WhPosInvStock> {
  invCode?: string; // 物料编码
  whcode?: string; // 仓库编码
  posCode?: string; // 货位编码
  cbatch?: string; // 批次
  iquantity?: number; // 现存量
  inum?: number; // 件数
  company?: string; // 公司
}

export const whPosInvStockList = (params?: WhPosInvStock | any) =>
  defHttp.get<WhPosInvStock>({ url: adminPath + '/bas/wh/stock/whPosInvStock/list', params });

export const whPosInvStockListData = (params?: WhPosInvStock | any) =>
  defHttp.post<Page<WhPosInvStock>>({ url: adminPath + '/bas/wh/stock/whPosInvStock/listData', params });

export const whPosInvStockForm = (params?: WhPosInvStock | any) =>
  defHttp.get<WhPosInvStock>({ url: adminPath + '/bas/wh/stock/whPosInvStock/form', params });

export const whPosInvStockSave = (params?: any, data?: WhPosInvStock | any) =>
  defHttp.postJson<WhPosInvStock>({ url: adminPath + '/bas/wh/stock/whPosInvStock/save', params, data });

export const whPosInvStockDelete = (params?: WhPosInvStock | any) =>
  defHttp.get<WhPosInvStock>({ url: adminPath + '/bas/wh/stock/whPosInvStock/delete', params });
