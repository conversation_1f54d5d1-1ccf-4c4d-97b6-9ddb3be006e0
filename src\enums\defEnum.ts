/**
 * @description: 条码来源类型枚举
 */

export enum BarTypeEnum {
  MoNotify = '1', // 入库通知
  PoOrder = '2', // 采购订单
  PuArr = '3',
  Inventory = '4', // 存货
  Position = '5',
  TuoPan = '6',
  InvBatch = '21',
}

export enum OfficeTypeEnum {
  ZH = '1', // 综合部门
  SC = '2', // 生产部门
  CW = '3', // 财务部门
  XS = '4', // 销售部门
  CG = '5', // 采购部门
}

// 保留小数位数
export enum NumEnum {
  scaleNum = 4, //件数
  scaleQty = 4, //数量
  scalePrice = 6, //单价
  scaleChangeRate = 4, //换算率
  scaleSelQty = 4 // 送货数小数
}

export enum replaceEnum {
  replace = '@@',
}
