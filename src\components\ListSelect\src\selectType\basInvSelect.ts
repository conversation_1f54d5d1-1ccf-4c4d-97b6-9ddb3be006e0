import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { basInvListData } from '/@/api/bas/inv/basInv';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('商品选择'),
};

const searchForm: FormProps = {
  showResetButton:false,
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('商品编码'),
      field: 'viewCode',
      component: 'Input',
    },
    {
      label: t('商品名称'),
      field: 'invName',
      component: 'Input',
    },
    {
      label: t('公司编码'),
      field: 'companyCode',
      component: 'Input',
      show: false,
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('商品编码'),
    dataIndex: 'viewCode',
    width: 100,
  },
  {
    title: t('商品名称'),
    dataIndex: 'invName',
    width: 100,
  },
  {
    title: t('单位'),
    dataIndex: 'funitname',
    width: 130,
  },
  {
    title: t('重量（g）'),
    dataIndex: 'weight',
    width: 130,
  },
  {
    title: t('公司编码'),
    dataIndex: 'companyCode',
    width: 130,
    ifShow: false,
  },
  {
    title: t('公司名称'),
    dataIndex: 'company.companyName',
    width: 130,
  },
];

const tableProps: BasicTableProps = {
  api: basInvListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'invCode',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'invCode',
  itemName: 'invName',
  isShowCode: true,
};
