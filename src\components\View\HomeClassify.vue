<template>
  <div class="banner">
    <template v-if="props.data.data && props.data.data.length > 0">
      <el-carousel height="160px" :interval="6000">
        <el-carousel-item v-for="(item, index) in list" :key="index">
          <!-- <img :src="item.url"> mode="aspectFill"-->
          <div class="swpierBox">
            <div v-for="(item1, index1) in item" :key="index1">
              <div class="img">
                <img
                  :src="
                    item1.picUrl
                      ? ctxPath + item1.picUrl
                      : '/src/assets/images/wu.jpg'
                  "
                />
                <!-- 'https://img.quanminyanxuan.com/other/21188f7a1e9340759c113aa569f96699.jpg?x-oss-process=image/resize,h_600,m_lfit' -->
              </div>
              <div class="text"> {{ item1.treeNames ? item1.treeNames : '名称' }} </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </template>
    <template v-else>
      <div class="image-null" style="width: 100%; height: 150px"
        ><span class="el-icon-picture"></span
      ></div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useGlobSetting } from '/@/hooks/setting';
  const { ctxPath } = useGlobSetting();
  const props = defineProps({
    data: { type: Object, default: {} },
  });
  let list = computed(() => {
    let arr = groupArray(props.data.data);
    // return view[0];
    console.log(arr, 'data======');
    return arr;
  });

  function groupArray(arr) {
    var groupedArr: any = [];
    for (var i = 0; i < arr.length; i += 4) {
      groupedArr.push(arr.slice(i, i + 4));
    }
    return groupedArr;
  }
</script>

<style lang="less" scoped>
  .banner {
    // font-size: 0;
    img {
      width: 100%;
    }
    .image-null {
      background: #ffffff;
      font-size: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #c1c1c1;
      width: 100%;
    }
    .el-carousel__indicator--horizontal {
      padding: 12px 4px;
    }
    .el-carousel__button {
      width: 12px;
    }
  }

  .swpierBox {
    height: 160px;
    display: flex;
    // padding: 10px;
    div {
      // display: flex;
      // flex-direction: column;
      width: 23%;
      height: 100%;
      margin: 0 5px;

      .img {
        margin-top: 10px;
        width: 100%;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
      }
      .text {
        width: 100%;
        font-size: 14px;
        text-align: center;
        overflow: hidden;
      }
    }
  }
</style>
