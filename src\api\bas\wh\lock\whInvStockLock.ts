/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhInvStockLock extends BasicModel<WhInvStockLock> {
  whcode?: string; // 仓库编码
  invCode?: string; // 物料编码
  iquantity?: number; // 现存量
  lockQty?: number; // 锁定数
  lockKey?: string; // 锁定key
  company?: string; // 公司
}

export const whInvStockLockList = (params?: WhInvStockLock | any) =>
  defHttp.get<WhInvStockLock>({ url: adminPath + '/bas/wh/lock/whInvStockLock/list', params });

export const whInvStockLockListData = (params?: WhInvStockLock | any) =>
  defHttp.post<Page<WhInvStockLock>>({ url: adminPath + '/bas/wh/lock/whInvStockLock/listData', params });

export const whInvStockLockForm = (params?: WhInvStockLock | any) =>
  defHttp.get<WhInvStockLock>({ url: adminPath + '/bas/wh/lock/whInvStockLock/form', params });

export const whInvStockLockSave = (params?: any, data?: WhInvStockLock | any) =>
  defHttp.postJson<WhInvStockLock>({ url: adminPath + '/bas/wh/lock/whInvStockLock/save', params, data });

export const whInvStockLockDelete = (params?: WhInvStockLock | any) =>
  defHttp.get<WhInvStockLock>({ url: adminPath + '/bas/wh/lock/whInvStockLock/delete', params });
