/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPosCard extends BasicModel<WhPosCard> {
  invCode?: string; // 物料编码
  whcode?: string; // 仓库编码
  posCode?: string; // 货位编码
  cbatch?: string; // 批次
  inQty?: number; // 收入数量
  outQty?: number; // 发出数量
  inum?: number; // 件数
  company?: string; // 公司
}

export const whPosCardList = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/bas/wh/card/whPosCard/list', params });

export const whPosCardListData = (params?: WhPosCard | any) =>
  defHttp.post<Page<WhPosCard>>({ url: adminPath + '/bas/wh/card/whPosCard/listData', params });

export const whPosCardForm = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/bas/wh/card/whPosCard/form', params });

export const whPosCardSave = (params?: any, data?: WhPosCard | any) =>
  defHttp.postJson<WhPosCard>({ url: adminPath + '/bas/wh/card/whPosCard/save', params, data });

export const whPosCardDelete = (params?: WhPosCard | any) =>
  defHttp.get<WhPosCard>({ url: adminPath + '/bas/wh/card/whPosCard/delete', params });
