<template>
  <div style="margin: 10px 0; width: 100%; min-height: 120px">
   
    <div style="padding: 10px" v-for="(item, index) in props.data.data" :key="index">
      <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px">产品基本信息</div>
      <p><span style="display: inline-block;width: 90px;color: #aaa;">产品名称：</span>{{ item.cinvname }}</p>
      <p><span style="display: inline-block;width: 90px;color: #aaa;">规格型号：</span>{{ item.cinvstd }}</p>
      <p><span style="display: inline-block;width: 90px;color: #aaa;">生产日期：</span>2025/2/26</p>
      <p><span style="display: inline-block;width: 90px;color: #aaa;">批次：</span>123456789</p>
      <!-- <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px">{{ item.cinvname }}</div>
      <div style="display: flex; justify-content: space-between">
        <div style="color: #aaa">规格：{{ item.cinvstd }}</div>
        <div style="background: bisque; padding: 5px; color: #ff874b; border-radius: 5px"
          >1{{ item.unitName }}起订</div
        >
      </div>
      <div>
        <span style="color: red; font-size: 20px">￥{{ item.fretailprice?item.fretailprice:'99.99' }}</span>
        <span>/{{ item.unitName }}</span>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    data: { type: Object, default: {} },
  });
  console.log(props.data, 'data======');
</script>

<style lang="less" scoped></style>
