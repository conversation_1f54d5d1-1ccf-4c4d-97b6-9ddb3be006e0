/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface JhPlanArea extends BasicModel<JhPlanArea> {
  cid?: string; // cid
  fbillno?: string; // 发货单号
  forderno?: string; // 订单号
  fsupplyid?: number; // 购货单位内码
  fsupplierno?: string; // 购货单位编码
  fsuppliername?: string; // 购货单位名称
  inv_code?: string; // 商品
  cpos_code?: string; // 货位编码
  cpos_name?: string; // 货位名称
  fqty?: number; // 数量
  cfree1?: string; // 形象刊
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
  company_code?: string; // 公司编码
  company_name?: string; // 公司名称
  create_by_name?: string; // 创建人名称
}

export const jhPlanAreaList = (params?: JhPlanArea | any) =>
  defHttp.get<JhPlanArea>({ url: adminPath + '/jh/plan/jhPlanArea/list', params });

export const jhPlanAreaListData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listData', params });

export const jhPlanAreaListDataGroup = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listDataGroup', params });

export const jhPlanAreaForm = (params?: JhPlanArea | any) =>
  defHttp.get<JhPlanArea>({ url: adminPath + '/jh/plan/jhPlanArea/form', params });

export const jhPlanAreaSave = (params?: any, data?: JhPlanArea | any) =>
  defHttp.postJson<JhPlanArea>({ url: adminPath + '/jh/plan/jhPlanArea/save', params, data });

export const jhPlanAreaDelete = (params?: JhPlanArea | any) =>
  defHttp.get<JhPlanArea>({ url: adminPath + '/jh/plan/jhPlanArea/delete', params });

export const jhPlanAreaListBhData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listBhData', params });

export const jhPlanAreaListXclData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listXclData', params });

export const jhPlanAreaListBhsData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listBhsData', params });

export const jhPlanAreaConfirm = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/confirm', params });

// 
export const jhPlanAreaSaveData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/saveData', params });

// /jh/plan/jhPlanArea/clearData

export const jhPlanAreaClearData = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/clearData', params });

export const listProspect = (params?: JhPlanArea | any) =>
  defHttp.post<Page<JhPlanArea>>({ url: adminPath + '/jh/plan/jhPlanArea/listProspect', params });
