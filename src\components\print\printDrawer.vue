<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="printPros.title"
    :showHeader="false"
    :showOkBtn="false"
    :cancelText="'关闭'"
    @register="registerDrawer"
    :width="printPros.width"
  >
    <iframe
      v-if="printPros.fileName"
      :src="`${ctxPath}/ureport/pdf/show?_u=file:${printPros.fileName}.ureport.xml&ids=${printPros.ids}&counts=${printPros.counts}&_n=${printPros.title}&uuid=${printPros.uuid}`"
      :style="{
        width: '100%',
        height: `${printPros.height}`,
      }"
    ></iframe>
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'PrintDrawer',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { useGlobSetting } from '/@/hooks/setting';
  import { defineComponent, ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  const { ctxPath } = useGlobSetting();
  //打印参数
  let printPros = ref<any>({
    title: '', //弹窗标题 默认=数据打印
    ids: '', //待打印数据ID “,”隔开 必填
    counts: '', // 打印张数
    height: '', //Drawer高度 默认=0.6
    width: '', //Drawer宽度 默认=0.6
    fileName: '', //打印模板文件名称 必填
  });

  const [registerDrawer, { setDrawerProps }] = useDrawerInner(async (param = {}) => {
    setDrawerProps({ loading: true });
    let clientH = document.documentElement.clientHeight;
    let DrawerHeight = clientH * (param.height ? param.height : 0.6) + 'px';
    let DrawerWidth = (param.width ? param.width : 0.6) * 100 + '%';

    printPros.value = {
      title: param.title ? param.title : '数据打印',
      ids: param.ids ? param.ids : '',
      counts: param.counts ? param.counts : '',
      height: DrawerHeight,
      width: DrawerWidth,
      uuid: new Date().getTime(),
      fileName: param.fileName,
    };
    setDrawerProps({ loading: false });
  });
</script>
<style lang="less">
  #ifrId {
    width: 100%;
    height: 800px;
  }
  /* .ant-drawer-header{
    display: none !important;
  } */
</style>
