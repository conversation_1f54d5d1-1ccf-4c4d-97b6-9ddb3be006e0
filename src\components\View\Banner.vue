<template>
    <div class="banner">
        <template v-if="props.data.data && props.data.data.length > 0">
            <el-carousel 
            height="300px" 
        >
            <el-carousel-item v-for="(item, index) in props.data.data" :key="index">
                <img :src="item.url">
            </el-carousel-item>
        </el-carousel>
        </template>
        <template v-else>
            <div class="image-null" style="width: 100%;height: 300px;"><span class="el-icon-picture"></span></div>
        </template>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    data: { type: Object, default: {} },
  });
  console.log(props.data,'data======');
  
</script>

<style lang="less" scoped>
.banner{
    // font-size: 0;
    img{
        width: 100%;
    }
    .image-null{
        background: #ffffff;
        font-size: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #c1c1c1;
        width: 100%;
    }
    .el-carousel__indicator--horizontal{
        padding: 12px 4px;
    }
    .el-carousel__button{
        width: 12px;
    }
    
}
</style>