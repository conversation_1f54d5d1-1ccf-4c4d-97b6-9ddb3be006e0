<template>
  <div class="product-content">
    <p class="tit">商品列表 <span>（可拖动排序）</span></p>
    <el-button class="add-btn" type="primary" @click="toggleSearchPopup"
      ><i class="el-icon-plus"></i> 添加商品</el-button
    >
      <vuedraggable :list="list.data" v-if="list.data && list.data.length > 0" class="list">
        <template #item="{ element, index }">
          <li class="item" :key="index" @click.stop="editSp(element,index)">
            <img
              :src="
                element.picUrl
                  ? ctxPath + element.picUrl
                  : '/src/assets/images/wu.jpg'
              "
            />
            <!-- 'https://img.quanminyanxuan.com/other/21188f7a1e9340759c113aa569f96699.jpg?x-oss-process=image/resize,h_600,m_lfit' -->
            <div>{{ element.cinvname ? element.cinvname:'名称' }}</div>
            <div class="el-icon-error" @click.stop="deleteItem(index)">
              <Icon icon="simple-line-icons:close" />
            </div>
            <!-- <i class="el-icon-error" @click="deleteItem(index)"></i> -->
          </li>
        </template>
      </vuedraggable>

    <!-- <div class="options">
      <el-form label-width="80px">
        <template v-for="(key, val, index) in options">
          <el-form-item :label="key" :key="index" v-if="loadingOption">
            <el-switch
              v-model="list['options'][val]"
              :name="val"
              @change="optionsChange(val, $event)"
            ></el-switch>
          </el-form-item>
        </template>
      </el-form>
    </div> -->

    <!-- <el-dialog title="添加商品"  v-model="show" @close="close">
      <el-form label-width="100px">
        <el-form-item label="选择商品">
          <el-select
            v-model="selectProduct"
            filterable
            remote
            reserve-keyword
            placeholder="请输入商品名称"
            :remote-method="searchProductList"
            @change="addProduct"
            :loading="loading"
          >
            <el-option
              v-for="item in productList"
              :key="item.productId"
              :label="item.productName"
              :value-key="item.productName"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirm">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <el-upload :http-request="upload" :show-file-list="false" multiple action style="display: none">
      <el-button ref="elupload" type="primary"> 添加图片 </el-button>
    </el-upload>
    <BasicModal
      @register="registerModal"
      :title="'添加商品'"
      width="40%"
      :showFooter="true"
      @ok="confirm"
    >
      <BasicForm @register="registerForm">
        <template #spbg>
          <div style="height: 120px;">

          <el-button v-if="selectItem.length && !selectItem[0].url" type="primary"  @click="addImage(null)" class="add-image">
            添加背景图
          </el-button>

          <div class="r-image" v-if="selectItem.length && selectItem[0].url" >
            <div @click="removeImage" class="el-icon-close">
              <Icon icon="simple-line-icons:close" />
            </div>
            <!-- <span @click="removeImage(index)" class="el-icon-close"></span> -->
            <div class="image-box" @click="addImage">
              <img :src="selectItem[0].url" />
              <!-- <span @click="addImage(index)" class="el-icon-edit-outline"></span> -->
            </div>
          </div>
        </div>


        </template>
      </BasicForm>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  // import { searchProduct } from '@/api/pageDecoration.js'
  import { reactive, ref, computed, onMounted ,watch } from 'vue';
  import vuedraggable from 'vuedraggable';
  import { Icon } from '/@/components/Icon';
  import { ElMessage } from 'element-plus';
  import { saveFile, getBase64WithFile } from '/@/api/shop/shop';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  const { ctxPath } = useGlobSetting();

  let imageIndex = ref<any>(null);
  let elupload = ref<any>(null);
  const inputFormSchemas: FormSchema[] = [
    {
      label: '产品',
      // basInvSelect
      field: 'invCode',
      fieldLabel: 'invName',
      component: 'ListSelect',
      componentProps: {
        selectType: 'basInvSelect',
        onSelect: (e) => {
          console.log(e, '==========');
          selectItem.value = e;
        },
      },
    },
    {
      label: '边框',
      field: 'spbg',
      component: 'Input',
      slot: 'spbg',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, getFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const props = defineProps({
    data: { type: Object, default: {} },
  });


  let list: any = ref({});
  // let productList: any = reactive([]);
  // let options: any = reactive({
  //   originalPrice: '划线价',
  //   goodRatio: '好评率',
  //   volumeStr: '销量数',
  // });
  // let loading: any = ref(false);
  // let show: any = ref(false);
  let selectItem: any = ref([]);
  // let selectProduct: any = ref('');
  // let loadingOption: any = ref(false);
  const emit = defineEmits(['changeTab']);
  onMounted(() => {
    list.value = props.data;
    console.log(list.value,'list=========');
    

    if (!props.data.tabType) {
      emit('changeTab', 2);
      //   this.$emit('changeTab', 2);
    }

    // 默认开启所有选项
    // for (let key in options) {
    //   if (props.data.options[key] == undefined) {
    //     list.options[key] = true;
    //     // this.$set(this.list.options, key, true);
    //   }
    // }
    // loadingOption.value = true;
  });
  const [registerModal, { openModal, closeModal }] = useModal();
  // function optionsChange(key, result) {
  //   list.options[key] = result;
  //   // this.$set(this.list.options, key, result);
  // }
  function deleteItem(index) {
    list.value.data.splice(index, 1);
  }
  // function searchProductList(productName) {
  //   productList = productList;
  // }
  async function confirm() {
    // let data = await getFieldsValue()


    if(selectItem.value[0]){

      if(imageIndex.value == null){
        list.value.data.push({...selectItem.value[0]});
      }else{
        list.value.data[imageIndex.value] = {...selectItem.value[0]};
      }
      console.log(list.value, 'list===');
      console.log(selectItem.value[0], 'selectItem.value[0]===');
      console.log(list.value.data, 'list.data,=====');
      await closeModal();
      await resetFields();
    }else{
      ElMessage.error('请先选择促销产品');
    }
    // list.data.push(selectItem.value);
    // selectItem.value = null;
    // selectProduct.value = '';
    // close();
  }
  async function  toggleSearchPopup() {
    imageIndex.value = null
    selectItem.value= []
    await openModal(true,'');
    await resetFields()
    // show.value = true;
  }

  async function editSp(item,index) {
    imageIndex.value = index
    console.log(item);
    selectItem.value= [{...item}]
    await openModal(true,'');
    await setFieldsValue({
      invCode:item.cinvcode,
      invName:item.cinvname,
    })
    // show.value = true;
  }

  function removeImage() {
    selectItem.value[0].url = ''
    selectItem.value[0].fileUpload = {}
  }
  function addImage() {
    elupload.value?.$.vnode.el?.click();
  }

  async function upload(params) {
    console.log(params, 'params======');

    // const len = list.value.data.length;

    // if (len >= len.value) {
    //   ElMessage.error(`最多添加${len.value}张图片!`);
    //   return;
    // }
    const file = params.file,
      fileType = file.type;

    const verifyList = [
      {
        text: '只能上传图片格式png、jpg、gif!',
        result: fileType.indexOf('image') != -1,
      },
      {
        text: '只能上传大小小于5M',
        result: file.size / 1024 / 1024 < 5,
      },
    ];

    for (let item of verifyList) {
      if (!item.result) {
        ElMessage.error(item.text);
        return;
      }
    }

    await getBase64WithFile(file).then(async ({ result: fileUrl }) => {
      const res = await saveFile({
        base64: fileUrl,
        // id: list.value.id,
        fileName: file.name,
        uploadType: 'image',
      });

      file.fileUpload = res.fileUpload;
      console.log(res);
      console.log(file.fileUpload, 'file.fileUpload+++++');
    });

    const form = new FormData();
    form.append('file', file);
    form.append('clientType', 'multipart/form-data');

    const index = imageIndex.value;
    const data = {
      name: file.name,
      url: URL.createObjectURL(file),
      form,
      fileUpload: file.fileUpload ? file.fileUpload : '',
    };

    selectItem.value[0] ={
      ...selectItem.value[0],
      ...data
    }
    // if (index !== null) {
    //   list.value.data[index] = data;
    //   // this.$set(list.data, index, data)
    // } else {
    //   list.value.data.push(data);
    // }
  }

  
  // function close() {
  //   show.value = false;
  //   selectItem.value = null;
  //   selectProduct.value = '';
  // }
  // function addProduct(data) {
  //   selectItem.value = data;
  // }

  // // 模拟产品列表
  // productList = [
  //   {
  //     productId: 3601,
  //     productName: '驼大大新疆正宗骆驼奶粉初乳骆驼乳粉蓝罐礼盒装120g*4罐',
  //     productImg: 'https://img.quanminyanxuan.com/excel/f6860885547648d9996474bbf21fdca9.jpg',
  //     productPrice: 299,
  //     originalPrice: 598,
  //     volumeStr: '741',
  //     goodRatio: 98,
  //   },
  //   {
  //     productId: 3268,
  //     productName: '百合28件套新骨质瓷餐具',
  //     productImg: 'https://img.quanminyanxuan.com/excel/185e7365f65543f2b4ebc67036d6a78f.jpg',
  //     productPrice: 370,
  //     originalPrice: 1388,
  //     volumeStr: '400',
  //     goodRatio: 99,
  //   },
  //   {
  //     productId: 3343,
  //     productName: '和商臻品槐花蜜250克/瓶',
  //     productImg: 'https://img.quanminyanxuan.com/excel/4626c8c628d04935b0262d04991416b2.jpg',
  //     productPrice: 34.5,
  //     originalPrice: 72,
  //     volumeStr: '258',
  //     goodRatio: 98,
  //   },
  //   {
  //     productId: 3330,
  //     productName: '鲍参翅肚浓羹350g袋装',
  //     productImg: 'https://img.quanminyanxuan.com/excel/58a0c968dc7d42c3ac21e09d1862aa6f.jpg',
  //     productPrice: 75,
  //     originalPrice: 128,
  //     volumeStr: '258',
  //     goodRatio: 98,
  //   },
  // ];
</script>

<style lang="less" scoped>
  .el-icon-error {
    position: absolute;
    right: -10px;
    top: -6px;
    color: red;
    // background: red;
    font-size: 20px;
    cursor: pointer;
    // display: none;
    z-index: 9999;
  }
  .product-content {
    .tit {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin: 18px 0;
      padding-bottom: 10px;
      border-bottom: 1px dashed #ddd;
    }
    .add-btn {
      width: calc(100% - 30px);
      height: 34px;
      line-height: 34px;
      padding: 0;
      font-size: 12px;
      margin-left: 15px;
      margin-top: 5px;
    }
    .list {
      display: flex;
      flex-wrap: wrap;
      padding: 12px;
      margin: 0;
      .item {
        width: 70px;
        // height: 70px;
        border-radius: 6px;
        margin: 4px;
        position: relative;
        transition: all 0.3s;
        list-style: none;
        img {
          width: 100%;
          // height: 100%;
          height: 70px;
          border-radius: 4px;
        }
        i {
          position: absolute;
          top: -6px;
          right: -6px;
          cursor: pointer;
          opacity: 0;
          transition: all 0.3s;
          color: red;
        }
        &::before {
          content: '';
          height: 100%;
          width: 100%;
          position: absolute;
          top: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 4px;
          opacity: 0;
          transition: all 0.3s;
        }
        &:hover {
          cursor: grab;
          &::before,
          i {
            opacity: 1;
          }
        }
      }
    }
    .options {
      padding: 15px;
      border-radius: 6px;
      .el-form {
        background: #f7f8f9;
        overflow: hidden;
        padding: 10px 0;
        .el-form-item {
          margin: 0;
          label {
            font-size: 12px;
          }
        }
      }
    }
  }

  .r-image {
          /* text-align: right; */
          .el-icon-close {
            padding-left: 90px;
            color: #999;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 6px;
            cursor: pointer;
            &:hover {
              color: red;
            }
          }
          .image-box {
            width: 100px;
            height: 120px;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              opacity: 0;
              transition: all 0.3s;
            }
            span {
              position: absolute;
              top: 50%;
              left: 50%;
              color: #fff;
              transform: translate(-50%, -50%);
              font-size: 20px;
              cursor: pointer;
              opacity: 0;
              transition: all 0.3s;
            }
            img {
              max-width: 100%;
            }
            &:hover {
              &::before,
              span {
                opacity: 1;
              }
            }
          }
        }
</style>
