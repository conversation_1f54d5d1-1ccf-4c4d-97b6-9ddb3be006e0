/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasInv extends BasicModel<BasInv> {
  invCode?: string; // ID
  extId?: string; // 物料内码
  viewCode?: string; // 物料代码
  invName?: string; // 物料名称
  invStd?: string; // 规格型号
  invClsCode?: string; // 物料分类
  f105?: string; // 期次
  f101?: string; // 出版社
  f102?: string; // 书/刊号
  f106?: string; // 期刊/增刊/图书
  f107?: string; // 读本/杂志
  funitname?: string; // 主计量单位名称
  funitnamef?: string; // 辅计量单位名称
  fstatus?: string; // 类型
  f109?: string; // 辅助属性类别
  fconversionrate?: number; // 换算率
  weight?: number; // 重量
  packQty?: number; // 包数
  companyCode?: string; // 公司编码
  basInvExtendList?: any[]; // 子表列表
}

export const basInvList = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/list', params });

export const basInvListData = (params?: BasInv | any) =>
  defHttp.post<Page<BasInv>>({ url: adminPath + '/bas/inv/basInv/listData', params });

export const basInvForm = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/form', params });

export const basInvSave = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/bas/inv/basInv/save', params, data });

export const basInvImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/inv/basInv/importData',
      onUploadProgress,
    },
    params,
  );

export const basInvImportExcelUpdateData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/inv/basInv/importExcelUpdateData',
      onUploadProgress,
    },
    params,
  );

export const basInvDisable = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/disable', params });

export const basInvEnable = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/enable', params });

export const basInvDelete = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/delete', params });

export const basInvDisableBatch = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/disableBatch', params });

export const basInvEnableBatch = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/basInv/enableBatch', params });

export const basInvSyn = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/wms/exter/syn/synBasInv', params });

export const basInvUpdateYfdh = (params?: BasInv | any) =>
  defHttp.post<BasInv>({ url: adminPath + '/bas/inv/basInv/updateYfdh', params });


