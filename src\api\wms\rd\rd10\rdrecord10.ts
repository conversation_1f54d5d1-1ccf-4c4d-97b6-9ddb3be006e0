/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rdrecord10 extends BasicModel<Rdrecord10> {
  fdate?: string; // 退料日期
  ftype?: string; // 业务类型
  fpomode?: string; // 采购方式
  fbillno?: string; // 单据编号
  fsupplyid?: string; // 供应商内码
  fsuppliername?: string; // 供应商名称
  fsupplierno?: string; // 供应商编码
  fexplanation?: string; // 备注
  rdrecords10List?: any[]; // 子表列表
}

export const rdrecord10List = (params?: Rdrecord10 | any) =>
  defHttp.get<Rdrecord10>({ url: adminPath + '/rd/rd10/rdrecord10/list', params });

export const rdrecord10ListData = (params?: Rdrecord10 | any) =>
  defHttp.post<Page<Rdrecord10>>({ url: adminPath + '/rd/rd10/rdrecord10/listData', params });

export const rdrecords10ListData = (params?: Rdrecord10 | any) =>
  defHttp.post<Page<Rdrecord10>>({ url: adminPath + '/rd/rd10/rdrecord10/rdrecords10ListData', params });

export const taskListData = (params?: Rdrecord10 | any) =>
  defHttp.post<Page<Rdrecord10>>({ url: adminPath + '/rd/rd10/rdrecord10/taskListData', params });


export const rdrecord10Form = (params?: Rdrecord10 | any) =>
  defHttp.get<Rdrecord10>({ url: adminPath + '/rd/rd10/rdrecord10/form', params });

export const rdrecord10Save = (params?: any, data?: Rdrecord10 | any) =>
  defHttp.postJson<Rdrecord10>({ url: adminPath + '/rd/rd10/rdrecord10/save', params, data });

export const rdrecord10ImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/rd/rd10/rdrecord10/importData',
      onUploadProgress,
    },
    params,
  );

export const rdrecord10Delete = (params?: Rdrecord10 | any) =>
  defHttp.get<Rdrecord10>({ url: adminPath + '/rd/rd10/rdrecord10/delete', params });

// 同步K3到货单
export const rdrecord10Syn = (params?: Rdrecord10 | any) =>
  defHttp.get<Rdrecord10>({ url: adminPath + '/wms/exter/syn/synRdRecord10', params });

// /rd/rd10/rdrecord10/harvestRevoke
export const harvestRevoke = (params?: Rdrecord10 | any) =>
  defHttp.post<Page<Rdrecord10>>({ url: adminPath + '/rd/rd10/rdrecord10/harvestRevoke', params });

export const materialRevoke = (params?: Rdrecord10 | any) =>
  defHttp.post<Page<Rdrecord10>>({ url: adminPath + '/rd/rd10/rdrecord10/materialRevoke', params });


// /wms/exter/syn/synMaterialReturn

export const synMaterialReturn = (params?: Rdrecord10 | any) =>
  defHttp.post<Rdrecord10>({ url: adminPath + '/wms/exter/syn/synMaterialReturn', params });

// /rd/rd10/rdrecord10/generateTask
export const generateTask = (params?: Rdrecord10 | any) =>
  defHttp.post<Rdrecord10>({ url: adminPath + '/rd/rd10/rdrecord10/generateTask', params });
