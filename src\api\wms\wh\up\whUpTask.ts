/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhUpTask extends BasicModel<WhUpTask> {
  cid?: string; // cid
  hid?: string; // 主表ID
  rkStatus?: string; // 状态
  fitemno?: string; // 存货编码
  fitemname?: string; // 存货名称
  cfree1?: string; // 形象刊
  cfree2?: string; // 自由项1
  cfree3?: string; // 自由项1
  f109?: string; // 辅助属性
  fstockid?: string; // 仓库ID
  fstockno?: string; // 仓库编码
  fstockname?: string; // 仓库名称
  fdate?: string; // 日期
  fbillno?: string; // 单据编码
}

export const whUpTaskList = (params?: WhUpTask | any) =>
  defHttp.get<WhUpTask>({ url: adminPath + '/wh/up/whUpTask/list', params });

export const whUpTaskListData = (params?: WhUpTask | any) =>
  defHttp.post<Page<WhUpTask>>({ url: adminPath + '/wh/up/whUpTask/listData', params });

export const whUpTaskForm = (params?: WhUpTask | any) =>
  defHttp.get<WhUpTask>({ url: adminPath + '/wh/up/whUpTask/form', params });

export const whUpTaskSave = (params?: any, data?: WhUpTask | any) =>
  defHttp.postJson<WhUpTask>({ url: adminPath + '/wh/up/whUpTask/save', params, data });

export const whUpTaskDelete = (params?: WhUpTask | any) =>
  defHttp.get<WhUpTask>({ url: adminPath + '/wh/up/whUpTask/delete', params });
