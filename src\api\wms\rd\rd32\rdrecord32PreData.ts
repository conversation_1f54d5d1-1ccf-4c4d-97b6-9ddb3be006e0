/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Rdrecord32PreData extends BasicModel<Rdrecord32PreData> {
  companyCode?: string; // 公司编码
  fbillno?: string; // 发货单号
  fperson?: string; // 收货联系人
  fphone?: string; // 电话
  faddress?: string; // 收货地址
  invcode?: string; // 存货ID
  forderno?: string; // 订单号
  forderentryid?: string; // 订单行号
  fitemid?: string; // 物料内码
  fitemno?: string; // 物料代码
  fitemname?: string; // 物料名称
  selQty?: number; // 备货数量
  fstockid?: string; // 仓库ID
  fstockno?: string; // 仓库编码
  fstockname?: string; // 仓库名称
  createByName?: string; // 备货人名称
}

export const rdrecord32PreDataList = (params?: Rdrecord32PreData | any) =>
  defHttp.get<Rdrecord32PreData>({ url: adminPath + '/rd/rd32/rdrecord32PreData/list', params });

export const rdrecord32PreDataListData = (params?: Rdrecord32PreData | any) =>
  defHttp.post<Page<Rdrecord32PreData>>({
    url: adminPath + '/rd/rd32/rdrecord32PreData/listData',
    params,
  });

export const rdrecord32PreDataGroupListData = (params?: Rdrecord32PreData | any) =>
  defHttp.post<Page<Rdrecord32PreData>>({
    url: adminPath + '/rd/rd32/rdrecord32PreData/groupListData',
    params,
  });

export const rdrecord32PreDataForm = (params?: Rdrecord32PreData | any) =>
  defHttp.get<Rdrecord32PreData>({ url: adminPath + '/rd/rd32/rdrecord32PreData/form', params });

export const rdrecord32PreDataSave = (params?: any, data?: Rdrecord32PreData | any) =>
  defHttp.postJson<Rdrecord32PreData>({
    url: adminPath + '/rd/rd32/rdrecord32PreData/save',
    params,
    data,
  });

export const rdrecord32PreDataDelete = (params?: Rdrecord32PreData | any) =>
  defHttp.get<Rdrecord32PreData>({ url: adminPath + '/rd/rd32/rdrecord32PreData/delete', params });

// 生成拣货单
export const rdrecord32PreDataCreateBill = (params?: Rdrecord32PreData | any) =>
  defHttp.postJson<Rdrecord32PreData>({
    url: adminPath + '/rd/rd32/rdrecord32PreData/createBill',
    params,
  });
