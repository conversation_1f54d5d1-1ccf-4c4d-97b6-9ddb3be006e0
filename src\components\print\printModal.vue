<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="printPros.title"
    :showOkBtn="false"
    :cancelText="'关闭'"
    @register="registerModal"
    :width="printPros.width"
  >
    <iframe
      v-if="printPros.fileName"
      :src="`${ctxPath}/ureport/pdf/show?_u=file:${printPros.fileName}.ureport.xml&ids=${printPros.ids}&counts=${printPros.counts}&_n=${printPros.title}&uuid=${printPros.uuid}`"
      :style="{
        width: '100%',
        height: `${printPros.height}`,
      }"
    ></iframe>
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'PrintModal',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { useGlobSetting } from '/@/hooks/setting';
  import { defineComponent, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  const { ctxPath } = useGlobSetting();
  //打印参数
  let printPros = ref<any>({
    title: '', //弹窗标题 默认=数据打印
    ids: '', //待打印数据ID “,”隔开 必填
    counts: '', // 打印张数
    height: '', //Modal高度 默认=0.6
    width: '', //Modal宽度 默认=0.6
    fileName: '', //打印模板文件名称 必填
  });

  const [registerModal, { setModalProps }] = useModalInner(async (param = {}) => {
    setModalProps({ loading: true });
    let clientH = document.documentElement.clientHeight;
    let modalHeight = clientH * (param.height ? param.height : 0.6) + 'px';
    let modalWidth = (param.width ? param.width : 0.6) * 100 + '%';

    printPros.value = {
      title: param.title ? param.title : '数据打印',
      ids: param.ids ? param.ids : '',
      counts: param.counts ? param.counts : '',
      height: modalHeight,
      width: modalWidth,
      // uuid: new Date().getTime(),
      uuid: param.uuid?param.uuid:new Date().getTime(),
      fileName: param.fileName,
    };
    setModalProps({ loading: false });
  });
</script>
<style lang="less">
  #ifrId {
    width: 100%;
    height: 800px;
  }
</style>
