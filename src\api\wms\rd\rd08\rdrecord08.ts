/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/src/utils/http/axios';
import { useGlobSetting } from '/src/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Rdrecord08 extends BasicModel<Rdrecord08> {
  fbillno?: string; // 单据编号
  ftype?: string; // 业务类型
  fdate?: string; // 日期
  fexplanation?: string; // 备注
  fbiller?: string; // 制单人
  iqty?: number; // 总数
  sourceFlag?: string; // 来源标识
  rkStatus?: string; // 入库状态
  rdrecords08List?: any[]; // 子表列表
}

export const rdrecord08List = (params?: Rdrecord08 | any) =>
  defHttp.get<Rdrecord08>({ url: adminPath + '/rd/rd08/rdrecord08/list', params });

export const rdrecord08ListData = (params?: Rdrecord08 | any) =>
  defHttp.post<Page<Rdrecord08>>({ url: adminPath + '/rd/rd08/rdrecord08/listData', params });

export const rdrecord08Form = (params?: Rdrecord08 | any) =>
  defHttp.get<Rdrecord08>({ url: adminPath + '/rd/rd08/rdrecord08/form', params });

export const rdrecord08Save = (params?: any, data?: Rdrecord08 | any) =>
  defHttp.postJson<Rdrecord08>({ url: adminPath + '/rd/rd08/rdrecord08/save', params, data });

export const rdrecord08Delete = (params?: Rdrecord08 | any) =>
  defHttp.get<Rdrecord08>({ url: adminPath + '/rd/rd08/rdrecord08/delete', params });

export const rdrecord08Syn = (params?: Rdrecord08 | any) =>
  defHttp.get<Rdrecord08>({ url: adminPath + '/wms/exter/syn/adjustAdd', params });

// /rd/rd08/rdrecord08/batchUpdateDate
export const batchUpdateDate = (params?: Rdrecord08 | any) =>
  defHttp.post<Page<Rdrecord08>>({
    url: adminPath + '/rd/rd08/rdrecord08/batchUpdateDate',
    params,
  });

export const generateTask = (params?: Rdrecord08 | any) =>
  defHttp.post<Page<Rdrecord08>>({
    url: adminPath + '/rd/rd08/rdrecord08/generateTask',
    params,
  });

export const rdrecords08ListData = (params?: Rdrecord08 | any) =>
  defHttp.post<Page<Rdrecord08>>({
    url: adminPath + '/rd/rd08/rdrecord08/rdrecords08ListData',
    params,
  });


// /rd/rd08/rdrecord08/rd08Revoke
export const rd08Revoke = (params?: Rdrecord08 | any) =>
  defHttp.post<Page<Rdrecord08>>({
    url: adminPath + '/rd/rd08/rdrecord08/rd08Revoke',
    params,
  });
