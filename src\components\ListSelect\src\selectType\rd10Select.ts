import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import {
  taskListData
} from '/@/api/wms/rd/rd10/rdrecord10';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('入库任务明细选择'),
};

const searchForm: FormProps = {
  showResetButton:false,
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('物料代码'),
      field: 'fitemno',
      component: 'Input',
    },
    {
      label: t('物料名称'),
      field: 'fitemname',
      component: 'Input',
    },
    {
      label: t('公司编码'),
      field: 'parent.companyCode',
      component: 'Input',
      show: false,
    },
    {
      label: t('仓库编码'),
      field: 'fstockid',
      component: 'Input',
      show: false,
    },
    {
      label: t('红蓝标志'),
      field: 'parent.bred',
      component: 'Select',
      componentProps: {
        dictType: 'wms_bred_status',
      },
      show: false,
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('物料代码'),
    dataIndex: 'basInv.viewCode',
    key: 'a.fitemno',
    sorter: true,
    width: 130,
    align: 'left',
  },
  {
    title: t('物料名称'),
    dataIndex: 'fitemname',
    key: 'a.fitemname',
    sorter: true,
    width: 220,
    align: 'left',
  },

  {
    title: t('公司'),
    dataIndex: 'parent.companyName',
    key: 'parent.company_name',
    sorter: true,
    width: 160,
    align: 'left',
  },
  {
    title: t('红蓝标志'),
    dataIndex: 'parent.bred',
    key: 'parent.bred',
    sorter: true,
    width: 90,
    align: 'center',
    dictType: 'wms_bred_status',
  },
  {
    title: t('单据编号'),
    dataIndex: 'parent.fbillno',
    key: 'parent.fbillno',
    sorter: true,
    width: 110,
    align: 'left',
    slot: 'firstColumn',

  },
  {
    title: t('单据日期'),
    dataIndex: 'parent.fdate',
    key: 'parent.fdate',
    sorter: true,
    width: 90,
    align: 'left',
  },
 
  {
    title: t('供应商编码'),
    dataIndex: 'parent.fsupplierno',
    key: 'parent.fsupplierno',
    sorter: true,
    width: 90,
    align: 'left',
  },
  {
    title: t('供应商名称'),
    dataIndex: 'parent.fsuppliername',
    key: 'parent.fsuppliername',
    sorter: true,
    width: 180,
    align: 'left',
  },
  

 
  {
    title: t('仓库名称'),
    dataIndex: 'fstockname',
    key: 'a.fstockname',
    sorter: true,
    width: 90,
    align: 'left',
  },
  {
    title: t('应收数量'),
    dataIndex: 'fqty',
    key: 'a.fqty',
    sorter: true,
    width: 90,
    align: 'center',
  },
  {
    title: t('实收数量'),
    dataIndex: 'sumRkQty',
    key: 'a.sum_rk_qty',
    sorter: true,
    width: 90,
    align: 'center',
  },
  {
    title: t('状态'),
    dataIndex: 'rkStatus',
    key: 'a.rk_status',
    sorter: true,
    width: 100,
    align: 'center',
    dictType: 'wms_rd10_status',
    fixed: 'right',
  },
];

const tableProps: BasicTableProps = {
  api: taskListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'id',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'fitemno',
  itemName: 'fitemname',
  isShowCode: true,
};
