/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';
import { Rdrecord10 } from "/@/api/wms/rd/rd10/rdrecord10";

const { ctxPath, adminPath } = useGlobSetting();

export interface Transferorder extends BasicModel<Transferorder> {
  fbillno?: string; // 单据编号
  ftype?: string; // 单据类型
  fdate?: string; // 日期
  fexplanation?: string; // 备注
  fbiller?: string; // 制单人
  iqty?: number; // 总数
  sourceFlag?: string; // 来源标识
  rkStatus?: string; // 状态
  invInfo?: string; // 存货汇总信息
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  bsend?: string; // 是否推送
  transferordersList?: any[]; // 子表列表
}

export const transferorderList = (params?: Transferorder | any) =>
  defHttp.get<Transferorder>({ url: adminPath + '/rd/db/transferorder/list', params });

export const transferorderListData = (params?: Transferorder | any) =>
  defHttp.post<Page<Transferorder>>({ url: adminPath + '/rd/db/transferorder/listData', params });

export const transferorderForm = (params?: Transferorder | any) =>
  defHttp.get<Transferorder>({ url: adminPath + '/rd/db/transferorder/form', params });

export const transferorderSave = (params?: any, data?: Transferorder | any) =>
  defHttp.postJson<Transferorder>({ url: adminPath + '/rd/db/transferorder/save', params, data });

export const transferorderImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/rd/db/transferorder/importData',
      onUploadProgress,
    },
    params,
  );

export const transferorderDelete = (params?: Transferorder | any) =>
  defHttp.get<Transferorder>({ url: adminPath + '/rd/db/transferorder/delete', params });

export const synTransferorder = (params?: Rdrecord10 | any) =>
  defHttp.post<Rdrecord10>({ url: adminPath + '/wms/exter/syn/synTransferorder', params });

// /rd/db/transferorder/generateTask
export const generateTask = (params?: Rdrecord10 | any) =>
  defHttp.post<Rdrecord10>({ url: adminPath + '/rd/db/transferorder/generateTask', params });

export const transferordersListData = (params?: Transferorder | any) =>
  defHttp.post<Page<Transferorder>>({ url: adminPath + '/rd/db/transferorder/transferordersListData', params });

// 
export const batchSend = (params?: Transferorder | any) =>
  defHttp.post<Page<Transferorder>>({ url: adminPath + '/rd/db/transferorder/batchSend', params });

export const transferorderRevoke = (params?: Transferorder | any) =>
  defHttp.post<Page<Transferorder>>({ url: adminPath + '/rd/db/transferorder/transferorderRevoke', params });

