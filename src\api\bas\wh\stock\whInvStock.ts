/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhInvStock extends BasicModel<WhInvStock> {
  invCode?: string; // 物料编码
  whcode?: string; // 仓库编码
  cbatch?: string; // 批次
  iquantity?: number; // 现存量
  inum?: number; // 件数
  bstopFlag?: string; // 冻结标志
  favaQuantity?: number; // 可用量
  company?: string; // 公司
  cfree1?: string; // 自由项1
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
}

export const whInvStockList = (params?: WhInvStock | any) =>
  defHttp.get<WhInvStock>({ url: adminPath + '/bas/wh/stock/whInvStock/list', params });

export const whInvStockListData = (params?: WhInvStock | any) =>
  defHttp.post<Page<WhInvStock>>({ url: adminPath + '/bas/wh/stock/whInvStock/listData', params });

export const whInvStockForm = (params?: WhInvStock | any) =>
  defHttp.get<WhInvStock>({ url: adminPath + '/bas/wh/stock/whInvStock/form', params });

export const whInvStockSave = (params?: any, data?: WhInvStock | any) =>
  defHttp.postJson<WhInvStock>({ url: adminPath + '/bas/wh/stock/whInvStock/save', params, data });

export const whInvStockDelete = (params?: WhInvStock | any) =>
  defHttp.get<WhInvStock>({ url: adminPath + '/bas/wh/stock/whInvStock/delete', params });
