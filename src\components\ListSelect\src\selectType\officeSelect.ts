import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { officeListData } from '/@/api/sys/office';

const { t } = useI18n('sys.office');

const modalProps = {
  title: t('部门选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('机构代码'),
      field: 'viewCode',
      component: 'Input',
    },
    {
      label: t('机构名称'),
      field: 'officeName',
      component: 'Input',
    },
    {
      label: t('状态'),
      field: 'status',
      component: 'Select',
      componentProps: {
        dictType: 'sys_search_status',
        allowClear: true,
      },
    },
    {
      label: t('负责人'),
      field: 'leader',
      component: 'Input',
    },
    {
      label: t('机构类型'),
      field: 'officeType',
      component: 'Select',
      componentProps: {
        dictType: 'sys_office_type',
        allowClear: true,
      },
    },
    {
      label: t('机构全称'),
      field: 'fullName',
      component: 'Input',
    },
    {
      label: t('办公电话'),
      field: 'phone',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('机构名称'),
    dataIndex: 'officeName',
    width: 230,
    align: 'left',
    slot: 'firstColumn',
  },
  {
    title: t('机构全称'),
    dataIndex: 'fullName',
    width: 130,
    align: 'left',
  },
  {
    title: t('公司'),
    dataIndex: 'company.companyName',
    width: 130,
    align: 'left',
  },
  {
    title: t('状态'),
    dataIndex: 'status',
    width: 80,
    align: 'center',
    dictType: 'sys_status',
  },
  {
    title: t('更新时间'),
    dataIndex: 'updateDate',
    width: 130,
    align: 'center',
  },
  {
    title: t('备注信息'),
    dataIndex: 'remarks',
    width: 130,
    align: 'left',
  },
];

const tableProps: BasicTableProps = {
  api: officeListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  isTreeTable: true,
  rowKey: 'officeCode',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'officeCode',
  itemName: 'officeName',
  isShowCode: true,
};


