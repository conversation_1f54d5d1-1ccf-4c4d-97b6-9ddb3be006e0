<template>
  <div class="product" :class="props.className">
    <template v-if="props.data.data && props.data.data.length > 0">
      <div class="product-item" v-for="(item, index) in props.data.data" :key="index">
        <div class="image">
          <img
              :src="
                item.picUrl
                  ? ctxPath +  item.picUrl
                  : '/src/assets/images/wu.jpg'
              "
            />
        </div>
        <div class="info">
          <div class="name">{{ item.abbName }}</div>
          <!-- <p class="num" style="color: #aaa">
            规格: {{ item.cinvstd }}
          </p> -->
          <p style="margin: 5px 0; ">
            <span
            >
              {{item.moq?item.moq:'1'}}包起订</span
            >
          </p>
          
          <div style="display: flex;justify-content: space-between;">
            <span v-if="item.fprice">￥
             <span style="font-size: 22px;font-weight: 900;"> {{ item.fprice }}</span>
            </span>
            <span v-if="!item.fprice"  style="font-size: 22px;font-weight: 900;">暂无单价</span>

            <div style="padding: 6px 15px;background: #ffaa00;border-radius: 15px;color: #fff;position: relative;top: -4px;">立即购买></div>
            <!-- <span v-if="options.originalPrice">￥{{ item.originalPrice }}</span> -->
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="product-item product-default" v-for="index in 1" :key="index">
        <div class="image">
          <img
            src="/src/assets/images/wu.jpg"
            alt=""
          />
          <!-- https://img.quanminyanxuan.com/other/21188f7a1e9340759c113aa569f96699.jpg?x-oss-process=image/resize,h_600,m_lfit -->
        </div>
        <div class="info">
          <p class="name">这是促销包名称</p>
          <!-- <p class="num" style="color: #aaa"> 规格:xxxxxxxxxxxx </p> -->
          <p style="margin: 5px 0; display: flex; justify-content: space-between">
            <span
              style="
                display: inline-block;
                background: bisque;
                padding: 5px;
                color: #ff874b;
                border-radius: 5px;
              "
            >
              1件起订</span
            >
            <Icon icon="simple-line-icons:basket" color="#1296db" :size="25" />
          </p>
          <p class="price">
            <span>￥99.99</span>
            <!-- <span>￥9.99</span> -->
          </p>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import { useGlobSetting } from '/@/hooks/setting';
  const { ctxPath } = useGlobSetting();
  import { reactive, ref, computed, onMounted } from 'vue';
  const props = defineProps({
    data: { type: Object, default: {} },
    className: { type: String, default: '' },
  });
  const options = computed(() => {
    return props.data.options;
  });
  const line = computed(() => {
    return options.volumeStr && options.goodRatio ? '| ' : '';
  });
</script>

<style lang="less" scoped>
  .product {
    display: flex;
    flex-wrap: wrap;
    padding: 4px 8px;
    box-sizing: border-box;
    * {
      box-sizing: border-box;
    }
    &.one .product-item {
      width: 100%;
      padding: 10px;
      display: flex;
      border-bottom: 1px dashed #eee;
      .image {
        width: 100px;
        border-radius: 5px;
        overflow: hidden;
        margin-right: 10px;
      }
      .info {
        padding: 0 5px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        .price {
          font-size: 20px;
          margin: 0;
        }
        .num {
          margin: 12px 0 0;
        }
      }
    }
    &.three .product-item {
      width: 33.33%;
      .info .price {
        font-size: 16px;
      }
      &.product-default:nth-of-type(3) {
        display: block;
      }
    }
    .product-item {
      // width: 50%;
      width: 100%;
      padding: 5px;
      &.product-default:nth-of-type(3) {
        display: none;
      }
      .image {
        font-size: 0;
        img {
          max-width: 100%;
        }
      }
      .info {
        padding: 10px 5px 0;
        .name {
          font-weight: 900;
          font-size: 18px;
          // margin: 0;
          color: #333;
          text-overflow: ellipsis;
          word-break: break-all;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          // height: 38px;
          // line-height: 18px;
        }
        .num {
          font-size: 12px;
          color: #d23000;
          font-weight: 600;
        }
        .price {
          font-weight: 600;
          margin: 12px 0 0;
          font-size: 18px;
          span:nth-of-type(1) {
            color: red;
          }
          span:nth-of-type(2) {
            color: #b5b5b5;
            font-weight: 400;
            font-size: 12px;
            margin-left: 4px;
            text-decoration: line-through;
          }
        }
      }
    }
  }
</style>
