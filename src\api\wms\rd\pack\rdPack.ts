/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface RdPack extends BasicModel<RdPack> {
  djno?: string; // 单据编号
  ddate?: string; // 单据日期
  packType?: string; // 拣货方式
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
  rdPackCList?: any[]; // 子表列表
}

export const rdPackList = (params?: RdPack | any) =>
  defHttp.get<RdPack>({ url: adminPath + '/rd/pack/rdPack/list', params });

export const rdPackListData = (params?: RdPack | any) =>
  defHttp.post<Page<RdPack>>({ url: adminPath + '/rd/pack/rdPack/listData', params });

export const rdPackForm = (params?: RdPack | any) =>
  defHttp.get<RdPack>({ url: adminPath + '/rd/pack/rdPack/form', params });

export const rdPackSave = (params?: any, data?: RdPack | any) =>
  defHttp.postJson<RdPack>({ url: adminPath + '/rd/pack/rdPack/save', params, data });

export const rdPackDelete = (params?: RdPack | any) =>
  defHttp.get<RdPack>({ url: adminPath + '/rd/pack/rdPack/delete', params });
