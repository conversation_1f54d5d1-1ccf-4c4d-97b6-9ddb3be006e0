/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPdStockBill extends BasicModel<WhPdStockBill> {
  djno?: string; // 单据号
  sourceId?: string; // 来源id
  sourceNo?: string; // 来源单号
  pdType?: string; // 盘点方式
  ddate?: string; // 单据日期
  pdStatus?: string; // 盘点状态
  iqty?: number; // 总数
  sumqty?: number; // 实盘总数
  whcode?: string; // 仓库
  poscode?: string; // 货位
  companyCode?: string; // 公司编码
  companyname?: string; // 公司名称
  whPdStockBillsList?: any[]; // 子表列表
}

export const whPdStockBillList = (params?: WhPdStockBill | any) =>
  defHttp.get<WhPdStockBill>({ url: adminPath + '/wh/pd/whPdStockBill/list', params });

export const whPdStockBillListData = (params?: WhPdStockBill | any) =>
  defHttp.post<Page<WhPdStockBill>>({ url: adminPath + '/wh/pd/whPdStockBill/listData', params });

export const whPdStockBillForm = (params?: WhPdStockBill | any) =>
  defHttp.get<WhPdStockBill>({ url: adminPath + '/wh/pd/whPdStockBill/form', params });

export const whPdStockBillSave = (params?: any, data?: WhPdStockBill | any) =>
  defHttp.postJson<WhPdStockBill>({ url: adminPath + '/wh/pd/whPdStockBill/save', params, data });

export const whPdStockBillDelete = (params?: WhPdStockBill | any) =>
  defHttp.get<WhPdStockBill>({ url: adminPath + '/wh/pd/whPdStockBill/delete', params });
// /wh/pd/whPdStockBill/createStockBill

export const createStockBill = (params?: WhPdStockBill | any) =>
  defHttp.post<WhPdStockBill>({ url: adminPath + '/wh/pd/whPdStockBill/createStockBill', params });

// whPdStockBillsListData
export const whPdStockBillsListData = (params?: WhPdStockBill | any) =>
  defHttp.post<Page<WhPdStockBill>>({
    url: adminPath + '/wh/pd/whPdStockBill/whPdStockBillsListData',
    params,
  });
export const createRdBill = (params?: WhPdStockBill | any) =>
  defHttp.post<Page<WhPdStockBill>>({
    url: adminPath + '/wh/pd/whPdStockBill/createRdBill',
    params,
  });

export const savePdQtyData = (params?: any, data?: WhPdStockBill | any) =>
  defHttp.postJson<WhPdStockBill>({
    url: adminPath + '/wh/pd/whPdStockBill/savePdQtyData',
    params,
    data,
  });
