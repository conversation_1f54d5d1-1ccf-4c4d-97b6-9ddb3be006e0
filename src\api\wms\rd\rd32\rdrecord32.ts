/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rdrecord32 extends BasicModel<Rdrecord32> {
  fbillno?: string; // 发货单号
  fdate?: string; // 发货日期
  fyfs?: string; // 发运方式
  fsupplyid?: number; // 购货单位内码
  fsupplierno?: string; // 购货单位编码
  fsuppliername?: string; // 购货单位名称
  fcontacts?: string; // 客户联系人
  fphone?: string; // 客户电话
  fdz?: string; // 到站
  fprovince?: string; // 省份
  fcity?: string; // 城市
  fifprinteger?: string; // 是否打印
  furgentlevel?: string; // 紧急程度
  fexplanation?: string; // 备注
  fdepartment?: string; // 部门
  femp?: string; // 业务员
  fempz?: string; // 主管
  fbiller?: string; // 制单
  fifpack?: string; // 是否打包
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  rdrecords32List?: any[]; // 子表列表
}

export const rdrecord32List = (params?: Rdrecord32 | any) =>
  defHttp.get<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/list', params });

export const rdrecord32ListData = (params?: Rdrecord32 | any) =>
  defHttp.post<Page<Rdrecord32>>({ url: adminPath + '/rd/rd32/rdrecord32/listData', params });


export const rdrecords32SubListData = (params?: Rdrecord32 | any) =>
  defHttp.post<Page<Rdrecord32>>({
    url: adminPath + '/rd/rd32/rdrecord32/rdrecords32ListData',
    params,
  });

export const rdrecord32Form = (params?: Rdrecord32 | any) =>
  defHttp.get<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/form', params });

export const rdrecord32Save = (params?: any, data?: Rdrecord32 | any) =>
  defHttp.postJson<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/save', params, data });

export const rdrecord32Delete = (params?: Rdrecord32 | any) =>
  defHttp.get<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/delete', params });

export const rdrecord32ImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/rd/rd32/rdrecord32/importData',
      onUploadProgress,
    },
    params,
  );

export const rdrecord32Syn = (params?: Rdrecord32 | any) =>
  defHttp.get<Rdrecord32>({ url: adminPath + '/wms/exter/syn/synRdRecord32', params });

export const rdrecord32SubListDataByGroup = (params?: Rdrecord32 | any) =>
  defHttp.post<Page<Rdrecord32>>({
    url: adminPath + '/rd/rd32/rdrecord32/subListDataByGroup',
    params,
  });

export const rdrecord32Revoke = (params?: Rdrecord32 | any) =>
  defHttp.post<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/orderRevoke', params });

export const findRd32NextBill = (params?: Rdrecord32 | any) =>
  defHttp.post<Rdrecord32>({ url: adminPath + '/rd/rd32/rdrecord32/findRd32NextBill', params });
