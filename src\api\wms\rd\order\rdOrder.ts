/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface RdOrder extends BasicModel<RdOrder> {
  djno?: string; // 出库单号
  fbillno?: string; // 发货单号
  fdate?: string; // 发货日期
  fyfs?: string; // 发运方式
  fsupplyid?: string; // 购货单位内码
  fsupplierno?: string; // 购货单位编码
  fsuppliername?: string; // 购货单位名称
  fcontacts?: string; // 客户联系人
  fphone?: string; // 客户电话
  fdz?: string; // 到站
  fprovince?: string; // 省份
  fcity?: string; // 城市
  fifprinteger?: string; // 是否打印
  furgentlevel?: string; // 紧急程度
  fexplanation?: string; // 备注
  fdepartment?: string; // 部门
  femp?: string; // 业务员
  fempz?: string; // 主管
  fbiller?: string; // 制单
  fifpack?: string; // 是否打包
  sourceFlag?: string; // 来源标识
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  rdOrderRdsList?: any[]; // 子表列表
}

export const rdOrderList = (params?: RdOrder | any) =>
  defHttp.get<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/list', params });

export const rdOrderListData = (params?: RdOrder | any) =>
  defHttp.post<Page<RdOrder>>({ url: adminPath + '/wms/rd/order/rdOrder/listData', params });

export const rdOrderSubListData = (params?: RdOrder | any) =>
  defHttp.post<Page<RdOrder>>({
    url: adminPath + '/wms/rd/order/rdOrder/rdOrderRdsListData',
    params,
  });

export const rdOrderForm = (params?: RdOrder | any) =>
  defHttp.get<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/form', params });

export const rdOrderSave = (params?: any, data?: RdOrder | any) =>
  defHttp.postJson<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/save', params, data });

export const rdOrderDelete = (params?: RdOrder | any) =>
  defHttp.get<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/delete', params });

export const rdOrderConfirm = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/confirm', params });

export const rdOrderBatchSend = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/batchSend', params });

// export const updateWlData = (params?: any, data?: RdOrder | any) =>
//   defHttp.postJson<RdOrder>({
//     url: adminPath + '/wms/rd/order/rdOrder/updateWlData',
//     params,
//     data,
//   });
export const updateWlData = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/updateWlData', params });

export const rdOrderImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wms/rd/order/rdOrder/importExcelUpdateData',
      onUploadProgress,
    },
    params,
  );

export const rdJzrqSave = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/rd/jzrq/rdJzrq/save', params });

export const rdJzrqForm = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/rd/jzrq/rdJzrq/form', params });

export const printFx = (params?: any, data?: RdOrder | any) =>
  defHttp.postJson<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/printFx', params, data });

// /wms/rd/order/rdOrder/getPrintFx
export const getPrintFx = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/getPrintFx', params });

// /wms/rd/order/rdOrder/savePrintFx
export const savePrintFx = (params?: any, data?: RdOrder | any) =>
  defHttp.postJson<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/savePrintFx', params, data });

// 标签历史
export const hisListData = (params?: RdOrder | any) =>
  defHttp.post<RdOrder>({ url: adminPath + '/wms/rd/order/rdOrder/hisListData', params });
