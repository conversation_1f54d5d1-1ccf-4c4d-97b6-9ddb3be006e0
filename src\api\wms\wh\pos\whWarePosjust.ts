/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhWarePosjust extends BasicModel<WhWarePosjust> {
  code?: string; // 调整单号
  ddate?: string; // 单据日期
  companyCode?: string; // 公司
  whcode?: string; // 仓库
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
  whWarePosjustRdsList?: any[]; // 子表列表
}

export const whWarePosjustList = (params?: WhWarePosjust | any) =>
  defHttp.get<WhWarePosjust>({ url: adminPath + '/wms/wh/pos/whWarePosjust/list', params });

export const whWarePosjustListData = (params?: WhWarePosjust | any) =>
  defHttp.post<Page<WhWarePosjust>>({
    url: adminPath + '/wms/wh/pos/whWarePosjust/listData',
    params,
  });

export const whWarePosjustSubListData = (params?: WhWarePosjust | any) =>
  defHttp.post<Page<WhWarePosjust>>({
    url: adminPath + '/wms/wh/pos/whWarePosjust/whWarePosjustRdsListData',
    params,
  });

export const whWarePosjustForm = (params?: WhWarePosjust | any) =>
  defHttp.get<WhWarePosjust>({ url: adminPath + '/wms/wh/pos/whWarePosjust/form', params });

export const whWarePosjustSave = (params?: any, data?: WhWarePosjust | any) =>
  defHttp.postJson<WhWarePosjust>({
    url: adminPath + '/wms/wh/pos/whWarePosjust/save',
    params,
    data,
  });

export const whWarePosjustDelete = (params?: WhWarePosjust | any) =>
  defHttp.get<WhWarePosjust>({ url: adminPath + '/wms/wh/pos/whWarePosjust/delete', params });
