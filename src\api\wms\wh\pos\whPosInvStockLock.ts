/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPosInvStockLock extends BasicModel<WhPosInvStockLock> {
  invCode?: string; // 物料编码
  whcode?: string; // 仓库编码
  posCode?: string; // 货位编码
  iqty?: number; // 占用量
  cfree1?: string; // 自由项1
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
  companyCode?: string; // 公司
}

export const whPosInvStockLockList = (params?: WhPosInvStockLock | any) =>
  defHttp.get<WhPosInvStockLock>({ url: adminPath + '/wms/wh/pos/whPosInvStockLock/list', params });

export const whPosInvStockLockListData = (params?: WhPosInvStockLock | any) =>
  defHttp.post<Page<WhPosInvStockLock>>({ url: adminPath + '/wms/wh/pos/whPosInvStockLock/listData', params });

export const whPosInvStockLockForm = (params?: WhPosInvStockLock | any) =>
  defHttp.get<WhPosInvStockLock>({ url: adminPath + '/wms/wh/pos/whPosInvStockLock/form', params });

export const whPosInvStockLockSave = (params?: any, data?: WhPosInvStockLock | any) =>
  defHttp.postJson<WhPosInvStockLock>({ url: adminPath + '/wms/wh/pos/whPosInvStockLock/save', params, data });

export const whPosInvStockLockDelete = (params?: WhPosInvStockLock | any) =>
  defHttp.get<WhPosInvStockLock>({ url: adminPath + '/wms/wh/pos/whPosInvStockLock/delete', params });
