/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface WhDownTaskMain extends BasicModel<WhDownTaskMain> {
  djno?: string; // 拣货单号
  date?: string; // 单据日期
  type?: string; // 拣货方式
  jhStatus?: string; // 拣货状态
  createByName?: string; // 创建人名称
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  sumQty?: number; // 总数
  sumPickQty?: number; // 累计拣货数
  printCount?: number; // 打印次数
  taskType?: string; // 任务类型
  whDownTaskDetailsList?: any[]; // 子表列表
}

export const whDownTaskMainList = (params?: WhDownTaskMain | any) =>
  defHttp.get<WhDownTaskMain>({ url: adminPath + '/wh/down/whDownTaskMain/list', params });

export const whDownTaskMainListData = (params?: WhDownTaskMain | any) =>
  defHttp.post<Page<WhDownTaskMain>>({
    url: adminPath + '/wh/down/whDownTaskMain/listData',
    params,
  });

export const whDownTaskMainForm = (params?: WhDownTaskMain | any) =>
  defHttp.get<WhDownTaskMain>({ url: adminPath + '/wh/down/whDownTaskMain/form', params });

export const whDownTaskMainSave = (params?: any, data?: WhDownTaskMain | any) =>
  defHttp.postJson<WhDownTaskMain>({
    url: adminPath + '/wh/down/whDownTaskMain/save',
    params,
    data,
  });

export const whDownTaskMainImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/down/whDownTaskMain/importData',
      onUploadProgress,
    },
    params,
  );

export const whDownTaskMainDelete = (params?: WhDownTaskMain | any) =>
  defHttp.get<WhDownTaskMain>({ url: adminPath + '/wh/down/whDownTaskMain/delete', params });

// /wh/down/whDownTaskMain/beachOver
export const whDownTaskMainBatchOverr = (params?: WhDownTaskMain | any) =>
  defHttp.post<Page<WhDownTaskMain>>({
    url: adminPath + '/wh/down/whDownTaskMain/batchOver',
    params,
  });

// /wh/down/whDownTaskDetailsPos/listData
export const whDownTaskDetailsPosListData = (params?: WhDownTaskMain | any) =>
  defHttp.post<Page<WhDownTaskMain>>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/listData',
    params,
  });

// whDownTaskDetailsListData
export const whDownTaskDetailsListData = (params?: WhDownTaskMain | any) =>
  defHttp.post<Page<WhDownTaskMain>>({
    url: adminPath + '/wh/down/whDownTaskMain/whDownTaskDetailsListData',
    params,
  });

export const updatePrintCount = (params?: WhDownTaskMain | any) =>
  defHttp.post<Page<WhDownTaskMain>>({
    url: adminPath + '/wh/down/whDownTaskMain/updatePrintCount',
    params,
  });
