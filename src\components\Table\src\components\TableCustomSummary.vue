<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <ATableSummary fixed>
    <!-- 当页合计行 -->
    <ATableSummaryRow>
      <!-- 为 row-selection 添加空的 summary cell -->
      <ATableSummaryCell
        v-if="hasRowSelection"
        :index="0"
        :style="{ textAlign: 'center', fontWeight: 'bold' }"
      >
        <!-- 选择列的 summary cell，通常为空 -->
      </ATableSummaryCell>
      <ATableSummaryCell
        v-for="(item, index) in summaryData"
        :key="index"
        :index="hasRowSelection ? index + 1 : index"
        :style="{ textAlign: item.align, fontWeight: 'bold' }"
      >
        {{ item.summaryValue }}
      </ATableSummaryCell>
    </ATableSummaryRow>
    
    <!-- 总计行 -->
    <ATableSummaryRow v-if="showTotalSummary">
      <!-- 为 row-selection 添加空的 summary cell -->
      <ATableSummaryCell
        v-if="hasRowSelection"
        :index="0"
        :style="{ textAlign: 'center', fontWeight: 'bold' }"
      >
        <!-- 选择列的 summary cell，通常为空 -->
      </ATableSummaryCell>
      <ATableSummaryCell
        v-for="(item, index) in summaryData"
        :key="index"
        :index="hasRowSelection ? index + 1 : index"
        :style="{ textAlign: item.align, fontWeight: 'bold', fixed: item.fixed }"
      >
        {{ item.summaryTotalValue }}
      </ATableSummaryCell>
    </ATableSummaryRow>
  </ATableSummary>
</template>

<script lang="ts">
import { defineComponent, computed, type PropType } from 'vue';
import { TableSummary, TableSummaryCell, TableSummaryRow } from 'ant-design-vue';
import type { BasicColumn } from '../types/table';
import { isArray } from '/@/utils/is';

export default defineComponent({
  name: 'TableCustomSummary',
  components: {
    ATableSummary: TableSummary,
    ATableSummaryCell: TableSummaryCell,
    ATableSummaryRow: TableSummaryRow,
  },
  props: {
    // 表格数据
    tableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 列配置
    columns: {
      type: Array as PropType<BasicColumn[]>,
      required: true,
      default: () => [],
    },
    // 是否有行选择
    hasRowSelection: {
      type: Boolean,
      default: false,
    },
    // 是否显示总计行
    showTotalSummary: {
      type: Boolean,
      default: false,
    },
    // 其他数据（用于总计）
    otherData: {
      type: Object,
      default: () => ({}),
    },
    // 合计标签显示的列索引
    summaryLabelColumnIndex: {
      type: Number,
      default: 1,
    },
    // 当页合计标签
    summaryLabel: {
      type: String,
      default: '当页合计',
    },
    // 总计标签
    totalLabel: {
      type: String,
      default: '总计',
    },
  },
  setup(props) {
    // 计算汇总数据
    const summaryData = computed(() => {
      const { tableData, columns, otherData, summaryLabelColumnIndex, summaryLabel, totalLabel } = props;
      
      if (!tableData || tableData.length === 0) {
        return [];
      }

      const processedColumns = [...columns];

      // 处理每一列的汇总
      processedColumns.forEach((column, columnIndex) => {
        // 设置合计标签
        if (columnIndex === summaryLabelColumnIndex) {
          column.summaryValue = summaryLabel;
          column.summaryTotalValue = totalLabel;
        } else {
          // 处理需要汇总的列
          if (column.showSummary) {
            const summaryValue = tableData.reduce((pre, cur) => {
              const value = getColumnValue(cur, column.dataIndex);
              if (value === undefined || value === null || value === '') {
                return pre;
              }
              const numValue = Number(value);
              if (isNaN(numValue)) {
                return pre;
              }
              return pre + numValue;
            }, 0);

            // 格式化当页汇总值
            if (typeof summaryValue === 'number' && !isNaN(summaryValue)) {
              if (column.dataIndex === 'fweight') {
                column.summaryValue = summaryValue.toFixed(2);
              } else {
                column.summaryValue = summaryValue.toFixed(0);
              }
            } else {
              column.summaryValue = '';
            }

            // 处理总计汇总
            if (
              column.showTotalSummary &&
              otherData &&
              otherData[column.dataIndex] !== undefined
            ) {
              const totalValue = otherData[column.dataIndex];
              if (typeof totalValue === 'number' && !isNaN(totalValue)) {
                if (column.dataIndex === 'fweight') {
                  column.summaryTotalValue = totalValue.toFixed(2);
                } else {
                  column.summaryTotalValue = totalValue;
                }
              } else {
                column.summaryTotalValue = '';
              }
            } else {
              column.summaryTotalValue = '';
            }
          } else {
            column.summaryValue = '';
            column.summaryTotalValue = '';
          }
        }
      });

      return processedColumns;
    });

    // 获取列值的辅助函数
    function getColumnValue(record: Recordable, dataIndex: string | string[]) {
      if (isArray(dataIndex)) {
        return dataIndex.reduce((pre, cur) => {
          if (pre && pre[cur] !== undefined) return pre[cur];
          return '';
        }, record);
      }
      return record[dataIndex];
    }

    return {
      summaryData,
    };
  },
});
</script>
