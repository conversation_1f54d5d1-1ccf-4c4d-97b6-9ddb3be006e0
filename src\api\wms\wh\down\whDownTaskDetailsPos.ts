/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';
import { UploadApiResult } from '/@/api/sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface WhDownTaskDetailsPos extends BasicModel<WhDownTaskDetailsPos> {
  hid?: string; // 父ID
  posCode?: string; // 货位编码
  posName?: string; // 货位名称
  iqty?: number; // 数量
  sumPickQty?: number; // 累计拣货数
  jhStatus?: string; // 拣货状态
  updateByName?: string; // 更新人名称
}

export const whDownTaskDetailsPosList = (params?: WhDownTaskDetailsPos | any) =>
  defHttp.get<WhDownTaskDetailsPos>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/list',
    params,
  });

export const whDownTaskDetailsPosListData = (params?: WhDownTaskDetailsPos | any) =>
  defHttp.post<Page<WhDownTaskDetailsPos>>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/listData',
    params,
  });

export const whDownTaskDetailsPosForm = (params?: WhDownTaskDetailsPos | any) =>
  defHttp.get<WhDownTaskDetailsPos>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/form',
    params,
  });

export const whDownTaskDetailsPosSave = (params?: any, data?: WhDownTaskDetailsPos | any) =>
  defHttp.postJson<WhDownTaskDetailsPos>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/save',
    params,
    data,
  });

export const whDownTaskDetailsPosImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/down/whDownTaskDetailsPos/importData',
      onUploadProgress,
    },
    params,
  );

export const whDownTaskDetailsPosDelete = (params?: WhDownTaskDetailsPos | any) =>
  defHttp.get<WhDownTaskDetailsPos>({
    url: adminPath + '/wh/down/whDownTaskDetailsPos/delete',
    params,
  });
