/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasCusCls extends TreeModel<BasCusCls> {
  code?: string; // 分类编码
  name?: string; // 分类名称
}

export const basCusClsList = (params?: BasCusCls | any) =>
  defHttp.get<BasCusCls>({ url: adminPath + '/bas/cus/cls/list', params });

export const basCusClsListData = (params?: BasCusCls | any) =>
  defHttp.post<BasCusCls[]>({ url: adminPath + '/bas/cus/cls/listData', params });

export const basCusClsForm = (params?: BasCusCls | any) =>
  defHttp.get<BasCusCls>({ url: adminPath + '/bas/cus/cls/form', params });

export const basCusClsCreateNextNode = (params?: BasCusCls | any) =>
  defHttp.get<BasCusCls>({ url: adminPath + '/bas/cus/cls/createNextNode', params });

export const basCusClsSave = (params?: any, data?: BasCusCls | any) =>
  defHttp.postJson<BasCusCls>({ url: adminPath + '/bas/cus/cls/save', params, data });

export const basCusClsDelete = (params?: BasCusCls | any) =>
  defHttp.get<BasCusCls>({ url: adminPath + '/bas/cus/cls/delete', params });

export const basCusClsTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/cus/cls/treeData', params });
