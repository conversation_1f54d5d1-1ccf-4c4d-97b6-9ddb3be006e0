/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface pkCorp extends BasicModel<pkCorp> {
  pkCorp?: string; // 公司编号
  
}

export const switchCorp = (params?: pkCorp | any) =>
  defHttp.post<pkCorp>({ url: adminPath + '/common/switchCorp', params });
export const getCorpCache = (params?: pkCorp | any) =>
  defHttp.post<pkCorp>({ url: adminPath + '/common/getCorpCache', params });


  
