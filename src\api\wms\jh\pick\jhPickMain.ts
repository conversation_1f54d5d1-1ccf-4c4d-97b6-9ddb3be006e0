/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface JhPickMain extends BasicModel<JhPickMain> {
  djno?: string; // 拣货单号
  date?: string; // 拣货日期
  type?: string; // 拣货方式
  jhStatus?: string; // 拣货状态
  whcode?: string; // 仓库
  createByName?: string; // 制单人名称
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  jhPickDetailsList?: any[]; // 子表列表
}

export const jhPickMainList = (params?: JhPickMain | any) =>
  defHttp.get<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/list', params });

export const jhPickMainListData = (params?: JhPickMain | any) =>
  defHttp.post<Page<JhPickMain>>({ url: adminPath + '/wms/jh/pick/jhPickMain/listData', params });

export const jhPickMainSubListData = (params?: JhPickMain | any) =>
  defHttp.post<Page<JhPickMain>>({
    url: adminPath + '/wms/jh/pick/jhPickMain/jhPickDetailsListData',
    params,
  });

export const jhPickMainForm = (params?: JhPickMain | any) =>
  defHttp.get<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/form', params });

export const jhPickMainSave = (params?: any, data?: JhPickMain | any) =>
  defHttp.postJson<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/save', params, data });

export const jhPickMainDelete = (params?: JhPickMain | any) =>
  defHttp.get<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/delete', params });

export const jhPickMainBachOver = (params?: JhPickMain | any) =>
  defHttp.get<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/bachOver', params });

export const findOrderCodeList = (params?: JhPickMain | any) =>
  defHttp.get<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/findOrderCodeList', params });

export const updatePrintCount = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/wms/jh/pick/jhPickMain/updatePrintCount', params });

export const findHandleCidsByJhMain = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleCidsByJhMain', params });

export const findHandleBhData = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleBhData', params });

export const findHandleWjhData = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleWjhData', params });

export const findHandleYjhData = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleYjhData', params });

export const findHandleYckData = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleYckData', params });

export const findHandleYtsData = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findHandleYtsData', params });


export const findCidsByOrder = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findCidsByOrder', params });


export const orderBack = (params?: any, data?: JhPickMain | any) =>
  defHttp.postJson<JhPickMain>({ url: adminPath + '/rd/rd32/handle/orderBack', params, data });


export const findCidsNotSendByOrder = (params?: JhPickMain | any) =>
  defHttp.post<JhPickMain>({ url: adminPath + '/rd/rd32/handle/findCidsNotSendByOrder', params });


export const orderRkBack = (params?: any, data?: JhPickMain | any) =>
  defHttp.postJson<JhPickMain>({ url: adminPath + '/rd/rd32/handle/orderRkBack', params, data });
