/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rdrecord01 extends BasicModel<Rdrecord01> {
  code?: string; // 入库单号
  companyCode?: string; // 公司编码
  venCode?: string; // 供应商编码
  venName?: string; // 供应商名称
  ddate?: string; // 单据日期
  preArrDate?: string; // 预计到达日期
  sourceCode?: string; // 来源单号
  whCode?: string; // 仓库
  officeName?: string; // 部门名称
  djStatus?: string; // 单据状态
  extCode?: string; // 外部系统单号
  extRs?: string; // 外部系统结果
  extDate?: string; // 外部系统时间
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
  rds01List?: any[]; // 子表列表
}

export const rd01List = (params?: Rdrecord01 | any) =>
  defHttp.get<Rdrecord01>({ url: adminPath + '/wh/rd01/rd01/list', params });

export const rd01ListData = (params?: Rdrecord01 | any) =>
  defHttp.post<Page<Rdrecord01>>({ url: adminPath + '/wh/rd01/rd01/listData', params });

export const rds01ListData = (params?: Rdrecord01 | any) =>
  defHttp.post<Page<Rdrecord01>>({ url: adminPath + '/wh/rd01/rd01/rds01ListData', params });

export const rd01Form = (params?: Rdrecord01 | any) =>
  defHttp.get<Rdrecord01>({ url: adminPath + '/wh/rd01/rd01/form', params });

export const rd01Save = (params?: any, data?: Rdrecord01 | any) =>
  defHttp.postJson<Rdrecord01>({ url: adminPath + '/wh/rd01/rd01/save', params, data });

export const rd01Delete = (params?: Rdrecord01 | any) =>
  defHttp.get<Rdrecord01>({ url: adminPath + '/wh/rd01/rd01/delete', params });

export const rd01ImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/wh/rd01/rd01/importData',
      onUploadProgress,
    },
    params,
  );
