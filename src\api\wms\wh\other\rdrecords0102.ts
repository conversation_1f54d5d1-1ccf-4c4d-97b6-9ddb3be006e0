/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface Rds0102 extends BasicModel<Rds0102> {
  code?: string; // 入库单号
  ddate?: string; // 单据日期
  busType?: string; // bus_type
  bred?: string; // bred
  companyCode?: string; // company_code
  whCode?: string; // 仓库
  createByName?: string; // 制单人名称
  updateByName?: string; // 修改人名称
}

export const findSummary0102 = (params?: Rds0102 | any) =>
  defHttp.get<Rds0102>({ url: adminPath + '/rd/rds0102/rdrecords0102/findSummary0102', params });

export const dynamicSummary = (params?: Rds0102 | any) =>
  defHttp.get<Rds0102>({ url: adminPath + '/rd/rds0102/rdrecords0102/dynamicSummary', params });
